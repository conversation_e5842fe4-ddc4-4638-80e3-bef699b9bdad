<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="ltr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @auth
    <meta name="user" content="{{ json_encode([
        'id' => auth()->user()->id,
        'name' => auth()->user()->first_name . ' ' . auth()->user()->last_name,
        'first_name' => auth()->user()->first_name,
        'last_name' => auth()->user()->last_name,
        'email' => auth()->user()->email
    ]) }}">
    @endauth

    <!-- SEO Meta Tags -->
    <title>@yield('title', __('common.admin_dashboard') . ' - ' . __('common.company_name'))</title>
    <meta name="description" content="@yield('meta_description', __('common.admin_dashboard_description'))">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Styles -->
    @vite(['resources/css/admin.css', 'resources/js/app.js'])

    <!-- Alpine.js for interactive components -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Additional Styles -->
    @stack('styles')
</head>
<body class="font-inter antialiased bg-neutral-50 text-primary-900">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-accent-600 text-white px-4 py-2 rounded-md z-50">
        Skip to main content
    </a>

    <div class="min-h-screen bg-neutral-50">
        <!-- Sidebar -->
        @include('partials.admin.sidebar')

        <!-- Main Content -->
        <div class="lg:ml-64 min-h-screen flex flex-col">
            <!-- Header -->
            @include('partials.admin.header')

            <!-- Page Content -->
            <main id="main-content" class="flex-1">
                @yield('content')
            </main>
        </div>
    </div>

    <!-- Mobile sidebar overlay -->
    <div class="fixed inset-0 bg-primary-900 bg-opacity-75 z-40 lg:hidden hidden" id="sidebar-overlay"></div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Chat Widget (will be initialized by JavaScript) -->
    <div id="chat-widget-container"></div>

    <!-- Inline Chat Widget Script -->
    <script src="{{ asset('js/chat-widget-standalone.js') }}"></script>

    <!-- Simple Rich Text Editor -->
    <script>
        // Simple Rich Text Editor Implementation
        class SimpleRichTextEditor {
            constructor(selector) {
                this.editors = document.querySelectorAll(selector);
                this.init();
            }

            init() {
                this.editors.forEach(textarea => {
                    this.createEditor(textarea);
                });
            }

            createEditor(textarea) {
                // Create wrapper
                const wrapper = document.createElement('div');
                wrapper.className = 'rich-text-editor border border-gray-300 rounded-lg overflow-hidden';
                
                // Create toolbar
                const toolbar = document.createElement('div');
                toolbar.className = 'flex items-center space-x-1 p-2 bg-gray-50 border-b border-gray-300';
                
                // Toolbar buttons
                const buttons = [
                    { command: 'bold', icon: 'B', title: 'Bold' },
                    { command: 'italic', icon: 'I', title: 'Italic' },
                    { command: 'underline', icon: 'U', title: 'Underline' },
                    { command: 'insertUnorderedList', icon: '•', title: 'Bullet List' },
                    { command: 'insertOrderedList', icon: '1.', title: 'Numbered List' },
                ];
                
                buttons.forEach(btn => {
                    const button = document.createElement('button');
                    button.type = 'button';
                    button.className = 'toolbar-btn';
                    button.innerHTML = btn.icon;
                    button.title = btn.title;
                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        document.execCommand(btn.command, false, null);
                    });
                    toolbar.appendChild(button);
                });
                
                // Create content editable div
                const editor = document.createElement('div');
                editor.contentEditable = true;
                editor.className = 'rich-text-content p-3 min-h-[120px] focus:outline-none';
                editor.innerHTML = textarea.value || '';
                
                // Sync content back to textarea
                editor.addEventListener('input', () => {
                    textarea.value = editor.innerHTML;
                });
                
                // Insert wrapper before textarea
                textarea.parentNode.insertBefore(wrapper, textarea);
                wrapper.appendChild(toolbar);
                wrapper.appendChild(editor);
                
                // Hide original textarea
                textarea.style.display = 'none';
            }
        }

        // Initialize rich text editors
        document.addEventListener('DOMContentLoaded', function() {
            new SimpleRichTextEditor('.rich-text');
        });
    </script>

    <style>
        .toolbar-btn {
            @apply p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors duration-150;
        }
        .toolbar-btn:hover {
            background-color: #f3f4f6;
        }
        .rich-text-content {
            font-family: Inter, Arial, sans-serif;
            font-size: 14px;
            line-height: 1.5;
        }
        .rich-text-content:focus {
            outline: 2px solid #3b82f6;
            outline-offset: -2px;
        }
        .rich-text-content ul, .rich-text-content ol {
            margin: 1em 0;
            padding-left: 2em;
        }
        .rich-text-content p {
            margin: 0.5em 0;
        }
        .rich-text-content strong {
            font-weight: bold;
        }
        .rich-text-content em {
            font-style: italic;
        }
        .rich-text-content u {
            text-decoration: underline;
        }
    </style>

    <!-- Scripts -->
    @stack('scripts')

    <!-- Dashboard JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const sidebar = document.getElementById('admin-sidebar');
            const overlay = document.getElementById('sidebar-overlay');

            if (mobileMenuButton && sidebar && overlay) {
                mobileMenuButton.addEventListener('click', function() {
                    sidebar.classList.toggle('-translate-x-full');
                    overlay.classList.toggle('hidden');
                });

                // Close sidebar when clicking overlay
                overlay.addEventListener('click', function() {
                    sidebar.classList.add('-translate-x-full');
                    overlay.classList.add('hidden');
                });
            }

            // CSRF token for AJAX requests
            window.Laravel = {
                csrfToken: '{{ csrf_token() }}'
            };

            // Toast notification system
            window.showToast = function(message, type = 'info', duration = 5000) {
                const container = document.getElementById('toast-container');
                const toast = document.createElement('div');
                
                const bgColor = {
                    'success': 'bg-green-500',
                    'error': 'bg-red-500',
                    'warning': 'bg-yellow-500',
                    'info': 'bg-blue-500'
                }[type] || 'bg-blue-500';
                
                toast.className = `${bgColor} text-white px-6 py-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full opacity-0`;
                toast.innerHTML = `
                    <div class="flex items-center justify-between">
                        <span>${message}</span>
                        <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                `;
                
                container.appendChild(toast);
                
                // Animate in
                setTimeout(() => {
                    toast.classList.remove('translate-x-full', 'opacity-0');
                }, 100);
                
                // Auto remove
                setTimeout(() => {
                    toast.classList.add('translate-x-full', 'opacity-0');
                    setTimeout(() => toast.remove(), 300);
                }, duration);
            };

            // Handle Laravel session flash messages
            @if(session('success'))
                showToast('{{ session('success') }}', 'success');
            @endif
            
            @if(session('error'))
                showToast('{{ session('error') }}', 'error');
            @endif
            
            @if(session('warning'))
                showToast('{{ session('warning') }}', 'warning');
            @endif
            
            @if(session('info'))
                showToast('{{ session('info') }}', 'info');
            @endif
        });
    </script>
</body>
</html>
