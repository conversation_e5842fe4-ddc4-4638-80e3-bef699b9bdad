<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Chat Webhooks Page...\n\n";

try {
    // Get admin user
    $adminRole = Role::where('name', 'admin')->first();
    $adminUser = User::where('email', '<EMAIL>')->first();
    
    if (!$adminUser) {
        $adminUser = User::create([
            'uuid' => \Str::uuid(),
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role_id' => $adminRole->id,
            'is_active' => true,
            'is_deleted' => false,
        ]);
    }
    
    echo "✅ Admin user ready (ID: {$adminUser->id})\n";

    // Test the webhooks page
    $request = Request::create('/admin/chat/webhooks', 'GET');
    $request->setUserResolver(function () use ($adminUser) {
        return $adminUser;
    });
    
    $response = $app->handle($request);
    $statusCode = $response->getStatusCode();
    
    if ($statusCode === 200) {
        echo "✅ [200] Chat Webhooks page is working correctly!\n";
        echo "   URL: /admin/chat/webhooks\n";
        echo "   Content length: " . strlen($response->getContent()) . " bytes\n";
    } elseif ($statusCode === 302) {
        echo "🔄 [302] Chat Webhooks page redirects (expected for auth middleware)\n";
        echo "   URL: /admin/chat/webhooks\n";
        echo "   Redirect to: " . $response->headers->get('Location') . "\n";
    } else {
        echo "❌ [{$statusCode}] Chat Webhooks page failed\n";
        echo "   URL: /admin/chat/webhooks\n";
        echo "   Content: " . substr($response->getContent(), 0, 200) . "...\n";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
