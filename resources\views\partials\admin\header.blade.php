{{-- Admin Dashboard Header --}}
@php
use Illuminate\Support\Facades\Storage;
@endphp
<header class="bg-white border-b border-neutral-200 sticky top-0 z-40 shadow-sm">
    <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
        {{-- Mobile menu button --}}
        <button type="button"
                class="lg:hidden p-2 rounded-lg text-primary-600 hover:text-primary-800 hover:bg-gradient-to-r hover:from-primary-50 hover:to-secondary-50 transition-all duration-200"
                id="mobile-menu-button">
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
        </button>

        {{-- Page Title & Breadcrumbs --}}
        <div class="flex-1 min-w-0 ml-4 lg:ml-0">
            <div class="flex items-center space-x-2">
                <h1 class="text-xl font-semibold text-primary-900 truncate">
                    @yield('page_title', __('common.admin_dashboard'))
                </h1>
                @hasSection('breadcrumbs')
                    <nav class="hidden sm:flex" aria-label="Breadcrumb">
                        <ol class="flex items-center space-x-2 text-sm text-primary-500">
                            <li>
                                <a href="{{ route('admin.dashboard') }}" class="hover:text-primary-700 transition-colors duration-200">
                                    Admin Dashboard
                                </a>
                            </li>
                            @yield('breadcrumbs')
                        </ol>
                    </nav>
                @endif
            </div>
        </div>

        {{-- Header Actions --}}
        <div class="flex items-center space-x-4">
            {{-- Search --}}
            <div class="hidden md:block relative">
                <div class="relative">
                    <input type="text"
                           id="admin-search"
                           placeholder="Search users, orders, projects..."
                           class="w-64 pl-10 pr-4 py-2 border border-neutral-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-4 w-4 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                </div>
                {{-- Search Results Dropdown --}}
                <div id="admin-search-results" class="absolute top-full left-0 right-0 mt-1 bg-white rounded-lg shadow-strong border border-neutral-200 max-h-64 overflow-y-auto hidden z-50">
                    <div class="p-3">
                        <div class="text-sm text-neutral-500">Start typing to search...</div>
                    </div>
                </div>
            </div>

            {{-- Notifications --}}
            <div class="relative">
                <button type="button"
                        class="p-2 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-lg transition-colors duration-200"
                        id="notifications-button">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-9A2.5 2.5 0 016.5 5h11A2.5 2.5 0 0120 7.5v3"/>
                    </svg>
                    {{-- Notification badge --}}
                    @php
                        $notificationCount = auth()->user()->unreadNotifications->count();
                    @endphp
                    @if($notificationCount > 0)
                        <span class="absolute -top-1 -right-1 h-4 w-4 bg-danger-500 text-white text-xs rounded-full flex items-center justify-center">
                            {{ $notificationCount > 9 ? '9+' : $notificationCount }}
                        </span>
                    @endif
                </button>

                {{-- Notifications dropdown (hidden by default) --}}
                <div class="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-strong border border-neutral-200 opacity-0 invisible transition-all duration-200"
                     id="notifications-dropdown">
                    <div class="p-4 border-b border-neutral-200">
                        <h3 class="text-sm font-semibold text-primary-900">Notifications</h3>
                    </div>
                    <div class="max-h-64 overflow-y-auto">
                        @php
                            $notifications = auth()->user()->unreadNotifications ?? collect();
                        @endphp

                        @if($notifications->count() > 0)
                            @foreach($notifications as $notification)
                                <div class="p-4 border-b border-neutral-100 hover:bg-gradient-to-r hover:from-neutral-50 hover:to-primary-50 transition-all duration-200 cursor-pointer">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-8 h-8 bg-gradient-to-br from-primary-100 to-primary-200 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0">
                                            <svg class="w-4 h-4 text-primary-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-9A2.5 2.5 0 016.5 5h11A2.5 2.5 0 0120 7.5v3"/>
                                            </svg>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm font-medium text-primary-900">{{ $notification->data['title'] ?? 'Notification' }}</p>
                                            <p class="text-sm text-neutral-600 mt-0.5">{{ $notification->data['message'] ?? 'You have a new notification' }}</p>
                                            <p class="text-xs text-neutral-500 mt-1">{{ $notification->created_at->diffForHumans() }}</p>
                                        </div>
                                        <div class="w-2 h-2 bg-primary-500 rounded-full flex-shrink-0 mt-2"></div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="p-8 text-center">
                                <div class="w-12 h-12 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <svg class="w-6 h-6 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 19H6.5A2.5 2.5 0 014 16.5v-9A2.5 2.5 0 016.5 5h11A2.5 2.5 0 0120 7.5v3"/>
                                    </svg>
                                </div>
                                <p class="text-sm text-neutral-500">No new notifications</p>
                            </div>
                        @endif
                    </div>
                    <div class="p-3 border-t border-neutral-200">
                        <a href="#" class="text-sm text-accent-600 hover:text-accent-700 font-medium transition-colors duration-200">
                            View all notifications
                        </a>
                    </div>
                </div>
            </div>

            {{-- User Menu --}}
            <div class="relative">
                <button type="button"
                        class="flex items-center space-x-3 p-2 rounded-lg hover:bg-primary-50 transition-colors duration-200"
                        id="user-menu-button">
                    @if(auth()->user()->avatar)
                        <img class="w-8 h-8 rounded-full object-cover border border-gray-200"
                             src="{{ Storage::url(auth()->user()->avatar) }}"
                             alt="{{ auth()->user()->full_name }}">
                    @else
                        <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-white">
                                {{ substr(auth()->user()->first_name, 0, 1) }}{{ substr(auth()->user()->last_name, 0, 1) }}
                            </span>
                        </div>
                    @endif
                    <div class="hidden md:block text-left">
                        <p class="text-sm font-medium text-primary-900">
                            {{ auth()->user()->first_name }}
                        </p>
                        <p class="text-xs text-primary-500">
                            {{ ucfirst(auth()->user()->role->name ?? 'admin') }}
                        </p>
                    </div>
                    <svg class="w-4 h-4 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                    </svg>
                </button>

                {{-- User dropdown menu --}}
                <div class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-strong border border-neutral-200 opacity-0 invisible transition-all duration-200"
                     id="user-menu-dropdown">
                    <div class="py-2">
                        <a href="{{ route('profile.edit') }}"
                           class="block px-4 py-2 text-sm text-primary-700 hover:bg-primary-50 hover:text-primary-900 transition-colors duration-200">
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                </svg>
                                <span>Profile Settings</span>
                            </div>
                        </a>
                        <a href="{{ route('admin.preferences.index') }}"
                           class="block px-4 py-2 text-sm text-primary-700 hover:bg-primary-50 hover:text-primary-900 transition-colors duration-200">
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.54 -.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c-.94.594-2.097.383-2.7-.081a1.724 1.724 0 00-1.065-2.572c1.756-.426 2.924-1.756 2.924-3.35a1.724 1.724 0 00-1.065-2.572c-.426-1.756-2.924-1.756-3.35 0a1.724 1.724 0 00-2.573 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.065c-.426-1.756 2.924-1.756 3.35 0z"/>
                                    </svg>
                                <span>User Preferences</span>
                            </div>
                        </a>
                        <a href="{{ route('home', ['locale' => app()->getLocale()]) }}"
                           class="block px-4 py-2 text-sm text-primary-700 hover:bg-primary-50 hover:text-primary-900 transition-colors duration-200">
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                                </svg>
                                <span>Back to Website</span>
                            </div>
                        </a>
                        <hr class="my-1 border-neutral-200">
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit"
                                    class="w-full text-left px-4 py-2 text-sm text-danger-600 hover:bg-danger-50 hover:text-danger-700 transition-colors duration-200">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                                    </svg>
                                    <span>Sign Out</span>
                                </div>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

{{-- Header JavaScript --}}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Notifications dropdown
    const notificationsButton = document.getElementById('notifications-button');
    const notificationsDropdown = document.getElementById('notifications-dropdown');

    if (notificationsButton && notificationsDropdown) {
        notificationsButton.addEventListener('click', function(e) {
            e.stopPropagation();
            notificationsDropdown.classList.toggle('opacity-0');
            notificationsDropdown.classList.toggle('invisible');
        });
    }

    // User menu dropdown
    const userMenuButton = document.getElementById('user-menu-button');
    const userMenuDropdown = document.getElementById('user-menu-dropdown');

    if (userMenuButton && userMenuDropdown) {
        userMenuButton.addEventListener('click', function(e) {
            e.stopPropagation();
            userMenuDropdown.classList.toggle('opacity-0');
            userMenuDropdown.classList.toggle('invisible');
        });
    }

    // Admin search functionality
    const searchInput = document.getElementById('admin-search');
    const searchResults = document.getElementById('admin-search-results');
    let searchTimeout;

    if (searchInput && searchResults) {
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            clearTimeout(searchTimeout);

            if (query.length < 2) {
                searchResults.classList.add('hidden');
                return;
            }

            searchTimeout = setTimeout(() => {
                const searchUrl = @json(route('admin.search'));
                fetch(`${searchUrl}?q=${encodeURIComponent(query)}`, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        credentials: 'same-origin'
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success && data.results) {
                            displaySearchResults(data.results);
                        } else {
                            displaySearchResults([]);
                        }
                    })
                    .catch(error => {
                        console.error('Search error:', error);
                        displaySearchResults([]);
                    });
            }, 300);
        });

        searchInput.addEventListener('focus', function() {
            if (this.value.trim().length >= 2) {
                searchResults.classList.remove('hidden');
            }
        });

        function displaySearchResults(results) {
            if (results.length === 0) {
                searchResults.innerHTML = '<div class="p-3"><div class="text-sm text-neutral-500">No results found</div></div>';
            } else {
                let html = '<div class="py-2">';
                results.forEach(result => {
                    const iconSvg = getIconSvg(result.icon || result.type);
                    const typeColor = getTypeColor(result.type);

                    html += `
                        <a href="${result.url}" class="block px-4 py-2 hover:bg-neutral-50 transition-colors duration-200">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-${typeColor}-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-${typeColor}-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        ${iconSvg}
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-primary-900">${result.title}</div>
                                    <div class="text-xs text-neutral-500">${result.description}</div>
                                    ${result.meta ? `<div class="text-xs text-neutral-400 mt-0.5">${formatMeta(result.meta)}</div>` : ''}
                                </div>
                                <div class="text-xs text-neutral-400 capitalize">${result.type.replace('_', ' ')}</div>
                            </div>
                        </a>
                    `;
                });
                html += '</div>';
                searchResults.innerHTML = html;
            }
            searchResults.classList.remove('hidden');
        }

        function getIconSvg(icon) {
            const icons = {
                'user': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>',
                'order': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>',
                'product': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>',
                'project': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>',
                'job': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6"/>',
                'document-text': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>'
            };
            return icons[icon] || icons['document-text'];
        }

        function getTypeColor(type) {
            const colors = {
                'user': 'blue',
                'order': 'green',
                'product': 'purple',
                'project': 'indigo',
                'job': 'yellow'
            };
            return colors[type] || 'gray';
        }

        function formatMeta(meta) {
            if (!meta) return '';
            const parts = [];
            if (meta.status) parts.push(meta.status);
            if (meta.created_at) parts.push(meta.created_at);
            return parts.join(' • ');
        }
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (notificationsDropdown && !notificationsButton.contains(e.target)) {
            notificationsDropdown.classList.add('opacity-0', 'invisible');
        }
        if (userMenuDropdown && !userMenuButton.contains(e.target)) {
            userMenuDropdown.classList.add('opacity-0', 'invisible');
        }
        if (searchResults && !searchInput.contains(e.target) && !searchResults.contains(e.target)) {
            searchResults.classList.add('hidden');
        }
    });
});
</script>
