<?php

namespace App\Console\Commands;

use App\Models\LoginHistory;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateExpiredSessions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'login-history:update-expired-sessions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update login history records for expired sessions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Updating expired sessions...');

        // Get session lifetime from config (in minutes)
        $sessionLifetime = config('session.lifetime', 120);
        $sessionLifetimeSeconds = $sessionLifetime * 60;

        // Find login history records that should have expired but don't have logout_at set
        $expiredSessions = LoginHistory::whereNull('logout_at')
            ->where('login_successful', true)
            ->where('login_at', '<=', now()->subMinutes($sessionLifetime))
            ->where('is_deleted', false)
            ->get();

        $updatedCount = 0;

        foreach ($expiredSessions as $session) {
            // Calculate the logout time as login_at + session lifetime
            $logoutAt = $session->login_at->addSeconds($sessionLifetimeSeconds);
            
            // Update the session with logout time and duration
            $session->update([
                'logout_at' => $logoutAt,
                'session_duration' => $sessionLifetimeSeconds,
            ]);

            $updatedCount++;
        }

        $this->info("Updated {$updatedCount} expired sessions.");

        // Also update sessions that have been inactive for more than the session lifetime
        // but have a more recent activity (this would require additional tracking)
        
        return Command::SUCCESS;
    }
}
