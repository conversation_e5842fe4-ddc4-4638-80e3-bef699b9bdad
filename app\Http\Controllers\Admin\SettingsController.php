<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use App\Services\ActivityLogger;

class SettingsController extends Controller
{
    protected ActivityLogger $activityLogger;

    public function __construct(ActivityLogger $activityLogger)
    {
        $this->activityLogger = $activityLogger;
    }

    /**
     * Display the settings dashboard.
     */
    public function index(): View
    {
        return view('admin.settings.index');
    }

    /**
     * Display general settings.
     */
    public function general(): View
    {
        return view('admin.settings.general');
    }

    /**
     * Update general settings.
     */
    public function updateGeneral(Request $request): RedirectResponse
    {
        $request->validate([
            'site_name' => 'required|string|max:255',
            'site_description' => 'nullable|string|max:500',
            'contact_email' => 'required|email',
            'contact_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
        ]);

        // Update settings logic here
        // This would typically update a settings table or config files

        $this->activityLogger->logActivity(
            'settings_updated',
            'General settings updated',
            $request->only(['site_name', 'site_description', 'contact_email', 'contact_phone', 'address'])
        );

        return redirect()->route('admin.settings.general')
            ->with('success', 'General settings updated successfully.');
    }

    /**
     * Display email settings.
     */
    public function email(): View
    {
        return view('admin.settings.email');
    }

    /**
     * Update email settings.
     */
    public function updateEmail(Request $request): RedirectResponse
    {
        $request->validate([
            'mail_driver' => 'required|string',
            'mail_host' => 'required|string',
            'mail_port' => 'required|integer',
            'mail_username' => 'required|string',
            'mail_password' => 'required|string',
            'mail_encryption' => 'nullable|string',
            'mail_from_address' => 'required|email',
            'mail_from_name' => 'required|string',
        ]);

        // Update email settings logic here

        $this->activityLogger->logActivity(
            'email_settings_updated',
            'Email settings updated',
            $request->except(['mail_password'])
        );

        return redirect()->route('admin.settings.email')
            ->with('success', 'Email settings updated successfully.');
    }

    /**
     * Display security settings.
     */
    public function security(): View
    {
        return view('admin.settings.security');
    }

    /**
     * Update security settings.
     */
    public function updateSecurity(Request $request): RedirectResponse
    {
        $request->validate([
            'login_attempts' => 'required|integer|min:1|max:10',
            'session_timeout' => 'required|integer|min:5|max:1440',
            'two_factor_enabled' => 'boolean',
            'password_expiry_days' => 'nullable|integer|min:30|max:365',
        ]);

        // Update security settings logic here

        $this->activityLogger->logActivity(
            'security_settings_updated',
            'Security settings updated',
            $request->all()
        );

        return redirect()->route('admin.settings.security')
            ->with('success', 'Security settings updated successfully.');
    }
}
