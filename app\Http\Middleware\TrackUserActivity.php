<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class TrackUserActivity
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only track activity for authenticated users
        if (Auth::check()) {
            $user = Auth::user();
            
            // Update last_seen_at timestamp
            $user->update(['last_seen_at' => now()]);
            
            // Optionally, you could also update the current login session's activity
            // This would help in calculating more accurate session durations
            $this->updateCurrentSessionActivity($user, $request);
        }

        return $response;
    }

    /**
     * Update the current session's activity timestamp.
     */
    protected function updateCurrentSessionActivity($user, Request $request): void
    {
        // Find the current active session for this user
        $sessionId = $request->session()->getId();
        
        if ($sessionId) {
            // You could store additional activity data here if needed
            // For now, we'll just rely on the last_seen_at timestamp
            
            // Optionally update additional_data with last activity timestamp
            $currentSession = $user->loginHistories()
                ->where('session_id', $sessionId)
                ->whereNull('logout_at')
                ->latest('login_at')
                ->first();

            if ($currentSession) {
                $additionalData = $currentSession->additional_data ?? [];
                $additionalData['last_activity'] = now()->toISOString();
                
                $currentSession->update([
                    'additional_data' => $additionalData
                ]);
            }
        }
    }
}
