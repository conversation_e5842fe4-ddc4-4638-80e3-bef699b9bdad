
<?php
use Illuminate\Support\Facades\Storage;
?>
<aside class="fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-neutral-200 transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 shadow-lg flex flex-col" id="admin-sidebar">
    
    <div class="flex items-center justify-center h-16 px-6 bg-gradient-to-r from-purple-600 to-purple-700 border-b border-purple-500">
        <a href="<?php echo e(route('admin.dashboard')); ?>" class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                <?php if(Storage::disk('public')->exists('images/logo.png')): ?>
                    <img src="<?php echo e(asset('storage/images/logo.png')); ?>" alt="<?php echo e(__('common.company_name')); ?>" class="w-6 h-6 object-contain">
                <?php else: ?>
                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8z" clip-rule="evenodd"></path>
                    </svg>
                <?php endif; ?>
            </div>
            <span class="text-white font-bold text-lg"><?php echo e(__('common.admin_panel')); ?></span>
        </a>
    </div>

    
    <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
        
        <a href="<?php echo e(route('admin.dashboard')); ?>" 
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.dashboard*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
            </svg>
            <?php echo e(__('common.dashboard')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.users.index')); ?>" 
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.users*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
            <?php echo e(__('common.users')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.projects.index')); ?>" 
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.projects*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
            <?php echo e(__('common.projects')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.services.index')); ?>" 
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.services*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
            </svg>
            <?php echo e(__('common.services')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.orders.index')); ?>" 
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.orders*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
            </svg>
            <?php echo e(__('common.orders')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.login-history.index')); ?>" 
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.login-history*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            <?php echo e(__('common.login_history')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.activity-logs.index')); ?>" 
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.activity-logs*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <?php echo e(__('common.activity_logs')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.email-campaigns.index')); ?>" 
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.email-campaigns*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            <?php echo e(__('common.email_campaigns')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.categories.index')); ?>"
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.categories*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
            </svg>
            <?php echo e(__('common.categories')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.products.index')); ?>"
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.products*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
            </svg>
            <?php echo e(__('common.products')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.coupons.index')); ?>"
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.coupons*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
            </svg>
            <?php echo e(__('common.coupons')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.jobs.index')); ?>"
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.jobs*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0H8"></path>
            </svg>
            <?php echo e(__('common.jobs')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.job-applications.index')); ?>"
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.job-applications*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <?php echo e(__('common.job_applications')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.project-applications.index')); ?>"
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.project-applications*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <?php echo e(__('common.project_applications')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.contact-submissions.index')); ?>"
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.contact-submissions*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            <?php echo e(__('common.contact_submissions')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.comments.index')); ?>"
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.comments*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            <?php echo e(__('common.comments')); ?>

        </a>

        
        <div class="relative" x-data="{ open: false }">
            <button @click="open = !open"
                    class="flex items-center justify-between w-full px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.chat*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
                <div class="flex items-center">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <?php echo e(__('common.chat_management')); ?>

                </div>
                <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
            <div x-show="open" x-transition class="ml-8 mt-2 space-y-1">
                <a href="<?php echo e(route('admin.chat.rooms.index')); ?>" class="block px-4 py-2 text-sm text-neutral-600 hover:text-purple-700 hover:bg-purple-50 rounded"><?php echo e(__('common.chat_rooms')); ?></a>
                <a href="<?php echo e(route('admin.chat.ai.index')); ?>" class="block px-4 py-2 text-sm text-neutral-600 hover:text-purple-700 hover:bg-purple-50 rounded"><?php echo e(__('common.ai_configuration')); ?></a>
                <a href="<?php echo e(route('admin.chat.analytics.index')); ?>" class="block px-4 py-2 text-sm text-neutral-600 hover:text-purple-700 hover:bg-purple-50 rounded"><?php echo e(__('common.chat_analytics')); ?></a>
                <a href="<?php echo e(route('admin.chat.moderation.index')); ?>" class="block px-4 py-2 text-sm text-neutral-600 hover:text-purple-700 hover:bg-purple-50 rounded"><?php echo e(__('common.chat_moderation')); ?></a>
                <a href="<?php echo e(route('admin.chat.training-data.index')); ?>" class="block px-4 py-2 text-sm text-neutral-600 hover:text-purple-700 hover:bg-purple-50 rounded"><?php echo e(__('common.training_data')); ?></a>
                <a href="<?php echo e(route('admin.chat.webhooks.index')); ?>" class="block px-4 py-2 text-sm text-neutral-600 hover:text-purple-700 hover:bg-purple-50 rounded"><?php echo e(__('common.webhooks')); ?></a>
                <a href="<?php echo e(route('admin.chat.ai.performance.index')); ?>" class="block px-4 py-2 text-sm text-neutral-600 hover:text-purple-700 hover:bg-purple-50 rounded"><?php echo e(__('common.ai_performance')); ?></a>
                <a href="<?php echo e(route('admin.chat.satisfaction.index')); ?>" class="block px-4 py-2 text-sm text-neutral-600 hover:text-purple-700 hover:bg-purple-50 rounded"><?php echo e(__('common.customer_satisfaction')); ?></a>
            </div>
        </div>

        
        <div class="relative" x-data="{ open: false }">
            <button @click="open = !open"
                    class="flex items-center justify-between w-full px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.email*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
                <div class="flex items-center">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    <?php echo e(__('common.email_management')); ?>

                </div>
                <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
            <div x-show="open" x-transition class="ml-8 mt-2 space-y-1">
                <a href="<?php echo e(route('admin.email-templates.index')); ?>" class="block px-4 py-2 text-sm text-neutral-600 hover:text-purple-700 hover:bg-purple-50 rounded"><?php echo e(__('common.email_templates')); ?></a>
                <a href="<?php echo e(route('admin.email-analytics.index')); ?>" class="block px-4 py-2 text-sm text-neutral-600 hover:text-purple-700 hover:bg-purple-50 rounded"><?php echo e(__('common.email_analytics')); ?></a>
                <a href="<?php echo e(route('admin.newsletter-subscriptions.index')); ?>" class="block px-4 py-2 text-sm text-neutral-600 hover:text-purple-700 hover:bg-purple-50 rounded"><?php echo e(__('common.newsletter_subscriptions')); ?></a>
                <a href="<?php echo e(route('admin.subscriber-segments.index')); ?>" class="block px-4 py-2 text-sm text-neutral-600 hover:text-purple-700 hover:bg-purple-50 rounded"><?php echo e(__('common.subscriber_segments')); ?></a>
                <a href="<?php echo e(route('admin.subscriber-tags.index')); ?>" class="block px-4 py-2 text-sm text-neutral-600 hover:text-purple-700 hover:bg-purple-50 rounded"><?php echo e(__('common.subscriber_tags')); ?></a>
            </div>
        </div>

        
        <a href="<?php echo e(route('admin.visitor-analytics.index')); ?>"
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.visitor-analytics*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            <?php echo e(__('common.visitor_analytics')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.permissions.index')); ?>"
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.permissions*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            <?php echo e(__('common.permissions')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.admin.ai-providers.index')); ?>"
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.ai-providers*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
            <?php echo e(__('common.ai_providers')); ?>

        </a>

        
        <a href="<?php echo e(route('admin.settings.index')); ?>"
           class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200 <?php echo e(request()->routeIs('admin.settings*') ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-600' : 'text-neutral-700 hover:bg-neutral-100'); ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <?php echo e(__('common.settings')); ?>

        </a>
    </nav>

    
    <div class="px-4 py-4 border-t border-neutral-200">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <span class="text-purple-600 font-medium text-sm">
                    <?php echo e(strtoupper(substr(auth()->user()->first_name, 0, 1))); ?><?php echo e(strtoupper(substr(auth()->user()->last_name, 0, 1))); ?>

                </span>
            </div>
            <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-neutral-900 truncate">
                    <?php echo e(auth()->user()->first_name); ?> <?php echo e(auth()->user()->last_name); ?>

                </p>
                <p class="text-xs text-neutral-500 truncate">
                    <?php echo e(__('common.admin')); ?>

                </p>
            </div>
        </div>
    </div>
</aside>
<?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/partials/admin/sidebar.blade.php ENDPATH**/ ?>