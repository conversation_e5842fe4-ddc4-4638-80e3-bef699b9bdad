<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Schedule sitemap generation
Schedule::command('seo:generate-sitemap')
    ->weekly()
    ->sundays()
    ->at('02:00')
    ->description('Generate comprehensive sitemap with all dynamic content');

// Schedule cache warming
Schedule::command('cache:warm')
    ->daily()
    ->at('01:00')
    ->description('Warm up application caches for better performance');

// Schedule login history cleanup for expired sessions
Schedule::command('login-history:update-expired-sessions')
    ->hourly()
    ->description('Update login history records for expired sessions');
