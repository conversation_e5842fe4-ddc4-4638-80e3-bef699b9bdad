<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Route;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing All Pages for 200 OK Responses...\n\n";

try {
    // 1. Get or create admin user
    $adminRole = Role::where('name', 'admin')->first();
    if (!$adminRole) {
        echo "❌ Admin role not found. Running seeders...\n";
        \Artisan::call('db:seed', ['--class' => 'RoleSeeder']);
        $adminRole = Role::where('name', 'admin')->first();
    }
    
    $adminUser = User::where('email', '<EMAIL>')->first();
    if (!$adminUser) {
        $adminUser = User::create([
            'uuid' => \Str::uuid(),
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role_id' => $adminRole->id,
            'is_active' => true,
            'is_deleted' => false,
        ]);
    }
    
    echo "✅ Admin user ready (ID: {$adminUser->id})\n\n";

    // 2. Define pages to test
    $pagesToTest = [
        // Admin Dashboard Pages
        ['GET', '/admin/dashboard', 'Admin Dashboard'],
        ['GET', '/admin/settings', 'Admin Settings'],
        ['GET', '/admin/services', 'Admin Services'],
        ['GET', '/admin/activity-logs', 'Activity Logs'],
        ['GET', '/admin/login-history', 'Login History'],
        ['GET', '/admin/contact-submissions', 'Contact Submissions'],
        ['GET', '/admin/newsletter-subscriptions', 'Newsletter Subscriptions'],
        ['GET', '/admin/visitor-analytics', 'Visitor Analytics'],
        ['GET', '/admin/users', 'Users Management'],
        ['GET', '/admin/products', 'Products Management'],
        ['GET', '/admin/orders', 'Orders Management'],
        ['GET', '/admin/projects', 'Projects Management'],
        ['GET', '/admin/categories', 'Categories Management'],
        ['GET', '/admin/coupons', 'Coupons Management'],
        ['GET', '/admin/jobs', 'Jobs Management'],
        ['GET', '/admin/job-applications', 'Job Applications'],
        ['GET', '/admin/project-applications', 'Project Applications'],
        ['GET', '/admin/permissions', 'Permissions Management'],
        ['GET', '/admin/preferences', 'User Preferences'],
        
        // API Endpoints
        ['GET', '/admin/dashboard/visitor-chart-data', 'Visitor Chart Data API'],
        ['GET', '/admin/dashboard/top-pages', 'Top Pages API'],
        ['GET', '/admin/activity-logs-data', 'Activity Logs Data API'],
        ['GET', '/admin/activity-logs-stats', 'Activity Logs Stats API'],
        
        // Chat Admin Pages
        ['GET', '/admin/chat/rooms', 'Chat Rooms'],
        ['GET', '/admin/chat/analytics', 'Chat Analytics'],
        ['GET', '/admin/chat/moderation', 'Chat Moderation'],
        ['GET', '/admin/chat/ai-performance', 'AI Performance'],
        ['GET', '/admin/chat/webhooks', 'Chat Webhooks'],
        
        // Public Pages (should work without authentication)
        ['GET', '/', 'Homepage'],
        ['GET', '/about', 'About Page'],
        ['GET', '/services', 'Services Page'],
        ['GET', '/contact', 'Contact Page'],
        ['GET', '/projects', 'Projects Page'],
        ['GET', '/careers', 'Careers Page'],
        ['GET', '/login', 'Login Page'],
        ['GET', '/register', 'Register Page'],
    ];

    $successCount = 0;
    $failureCount = 0;
    $failures = [];

    echo "Testing " . count($pagesToTest) . " pages...\n\n";

    foreach ($pagesToTest as $index => $pageTest) {
        [$method, $url, $description] = $pageTest;
        
        try {
            // Create request
            $request = Request::create($url, $method);
            
            // For admin pages, authenticate as admin
            if (str_starts_with($url, '/admin')) {
                $request->setUserResolver(function () use ($adminUser) {
                    return $adminUser;
                });
            }
            
            // Dispatch request through Laravel
            $response = $app->handle($request);
            $statusCode = $response->getStatusCode();
            
            if ($statusCode === 200) {
                echo "✅ [{$statusCode}] {$description} ({$url})\n";
                $successCount++;
            } elseif ($statusCode === 302 || $statusCode === 301) {
                echo "🔄 [{$statusCode}] {$description} ({$url}) - Redirect\n";
                $successCount++; // Redirects are often expected
            } else {
                echo "❌ [{$statusCode}] {$description} ({$url})\n";
                $failureCount++;
                $failures[] = [
                    'url' => $url,
                    'description' => $description,
                    'status' => $statusCode,
                    'content' => substr($response->getContent(), 0, 200) . '...'
                ];
            }
            
        } catch (Exception $e) {
            echo "💥 [ERROR] {$description} ({$url}) - {$e->getMessage()}\n";
            $failureCount++;
            $failures[] = [
                'url' => $url,
                'description' => $description,
                'status' => 'EXCEPTION',
                'content' => $e->getMessage()
            ];
        }
        
        // Small delay to prevent overwhelming the system
        usleep(100000); // 0.1 seconds
    }

    echo "\n" . str_repeat("=", 60) . "\n";
    echo "SUMMARY:\n";
    echo "✅ Successful: {$successCount}\n";
    echo "❌ Failed: {$failureCount}\n";
    echo "📊 Total: " . ($successCount + $failureCount) . "\n";
    echo "🎯 Success Rate: " . round(($successCount / ($successCount + $failureCount)) * 100, 1) . "%\n";

    if (!empty($failures)) {
        echo "\n" . str_repeat("-", 60) . "\n";
        echo "FAILURES DETAILS:\n";
        foreach ($failures as $failure) {
            echo "\n❌ {$failure['description']} ({$failure['url']})\n";
            echo "   Status: {$failure['status']}\n";
            echo "   Content: {$failure['content']}\n";
        }
    }

    if ($failureCount === 0) {
        echo "\n🎉 All pages are working correctly!\n";
    } else {
        echo "\n⚠️  Some pages need attention.\n";
    }

} catch (Exception $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
