<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Testing All Pages Accessibility\n";
echo "===============================\n\n";

// Find or create admin user
$adminRole = Role::where('name', 'admin')->first();
if (!$adminRole) {
    $adminRole = Role::create([
        'name' => 'admin',
        'slug' => 'admin',
        'description' => 'Administrator role',
        'is_active' => true,
        'permissions' => ['*']
    ]);
}

$admin = User::whereHas('role', function($q) {
    $q->where('name', 'admin');
})->first();

if (!$admin) {
    $admin = User::create([
        'first_name' => 'Test',
        'last_name' => 'Admin',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'role_id' => $adminRole->id,
        'email_verified_at' => now(),
        'is_active' => true,
        'uuid' => \Illuminate\Support\Str::uuid(),
    ]);
}

echo "Using admin user: {$admin->email}\n\n";

// List of routes to test
$routesToTest = [
    // Admin Dashboard Routes
    ['route' => 'admin.dashboard.index', 'name' => 'Admin Dashboard'],
    ['route' => 'admin.settings.index', 'name' => 'Admin Settings'],
    ['route' => 'admin.services.index', 'name' => 'Admin Services'],
    ['route' => 'admin.login-history.index', 'name' => 'Login History'],
    ['route' => 'admin.contact-submissions.index', 'name' => 'Contact Submissions'],
    ['route' => 'admin.newsletter-subscriptions.index', 'name' => 'Newsletter Subscriptions'],
    ['route' => 'admin.visitor-analytics.index', 'name' => 'Visitor Analytics'],
    ['route' => 'admin.chat.ai.performance.index', 'name' => 'AI Performance Dashboard'],
    
    // API Routes (should return JSON)
    ['route' => 'admin.dashboard.visitor-chart-data', 'name' => 'Dashboard Visitor Chart Data', 'type' => 'api'],
    ['route' => 'admin.dashboard.top-pages', 'name' => 'Dashboard Top Pages', 'type' => 'api'],
];

$passedTests = 0;
$failedTests = 0;
$totalTests = count($routesToTest);

foreach ($routesToTest as $test) {
    echo "Testing: {$test['name']} ({$test['route']})... ";
    
    try {
        // Check if route exists
        if (!Route::has($test['route'])) {
            echo "❌ FAILED - Route not found\n";
            $failedTests++;
            continue;
        }
        
        // Create a test request
        $url = route($test['route']);
        $request = Request::create($url, 'GET');
        
        // Set up session and authentication
        $session = app('session.store');
        $session->setId('test-session-' . uniqid());
        $session->start();
        $request->setLaravelSession($session);
        
        // Authenticate as admin
        auth()->login($admin);
        
        // Make the request
        $response = app()->handle($request);
        $statusCode = $response->getStatusCode();
        
        if ($statusCode === 200) {
            echo "✅ PASSED (200 OK)\n";
            $passedTests++;
        } elseif ($statusCode === 302) {
            echo "⚠️  REDIRECT ({$statusCode})\n";
            $passedTests++; // Redirects are often acceptable
        } else {
            echo "❌ FAILED ({$statusCode})\n";
            $failedTests++;
        }
        
    } catch (Exception $e) {
        echo "❌ FAILED - Exception: " . $e->getMessage() . "\n";
        $failedTests++;
    }
    
    // Clear authentication for next test
    auth()->logout();
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "TEST SUMMARY\n";
echo str_repeat("=", 50) . "\n";
echo "Total Tests: {$totalTests}\n";
echo "Passed: {$passedTests}\n";
echo "Failed: {$failedTests}\n";
echo "Success Rate: " . round(($passedTests / $totalTests) * 100, 2) . "%\n";

if ($failedTests === 0) {
    echo "\n🎉 ALL TESTS PASSED! All pages are accessible.\n";
} else {
    echo "\n⚠️  Some tests failed. Please check the failed routes.\n";
}
