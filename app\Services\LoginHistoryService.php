<?php

namespace App\Services;

use App\Models\LoginHistory;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Jenssegers\Agent\Agent;

class LoginHistoryService
{
    protected Agent $agent;

    public function __construct()
    {
        $this->agent = new Agent();
    }

    /**
     * Record a login attempt.
     */
    public function recordLogin(
        User $user,
        Request $request,
        bool $successful = true,
        string $method = 'email',
        string $failureReason = null
    ): LoginHistory {
        $loginData = $this->gatherLoginData($request, $user);
        
        return LoginHistory::create([
            'user_id' => $user->id,
            'login_at' => now(),
            'login_successful' => $successful,
            'login_method' => $method,
            'failure_reason' => $failureReason,
            'ip_address' => $loginData['ip_address'],
            'user_agent' => $loginData['user_agent'],
            'device_fingerprint' => $loginData['device_fingerprint'],
            'device_type' => $loginData['device_type'],
            'device_name' => $loginData['device_name'],
            'browser_name' => $loginData['browser_name'],
            'browser_version' => $loginData['browser_version'],
            'operating_system' => $loginData['operating_system'],
            'platform' => $loginData['platform'],
            'country' => $loginData['country'],
            'country_code' => $loginData['country_code'],
            'region' => $loginData['region'],
            'city' => $loginData['city'],
            'timezone' => $loginData['timezone'],
            'latitude' => $loginData['latitude'],
            'longitude' => $loginData['longitude'],
            'session_id' => $request->hasSession() ? $request->session()->getId() : null,
            'is_new_device' => $this->isNewDevice($user, $loginData['device_fingerprint']),
            'is_new_location' => $this->isNewLocation($user, $loginData['ip_address']),
            'is_suspicious' => $this->isSuspiciousLogin($user, $loginData),
            'additional_data' => $this->gatherAdditionalData($request),
        ]);
    }

    /**
     * Record logout.
     */
    public function recordLogout(User $user, Request $request): void
    {
        $sessionId = $request->hasSession() ? $request->session()->getId() : null;
        
        $loginHistory = LoginHistory::where('user_id', $user->id)
            ->where('session_id', $sessionId)
            ->whereNull('logout_at')
            ->latest('login_at')
            ->first();

        if ($loginHistory) {
            $sessionDuration = now()->diffInSeconds($loginHistory->login_at);
            
            $loginHistory->update([
                'logout_at' => now(),
                'session_duration' => $sessionDuration,
            ]);
        }
    }

    /**
     * Gather comprehensive login data.
     */
    protected function gatherLoginData(Request $request, User $user): array
    {
        $userAgent = $request->userAgent() ?: 'Unknown User Agent';
        $this->agent->setUserAgent($userAgent);
        $ipAddress = $this->getClientIpAddress($request);
        $locationData = $this->getLocationData($ipAddress);

        return [
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'device_fingerprint' => $this->generateDeviceFingerprint($request),
            'device_type' => $this->getDeviceType(),
            'device_name' => $this->getDeviceName(),
            'browser_name' => $this->agent->browser(),
            'browser_version' => $this->agent->version($this->agent->browser()),
            'operating_system' => $this->agent->platform(),
            'platform' => $this->agent->platform(),
            'country' => $locationData['country'] ?? null,
            'country_code' => $locationData['country_code'] ?? null,
            'region' => $locationData['region'] ?? null,
            'city' => $locationData['city'] ?? null,
            'timezone' => $locationData['timezone'] ?? null,
            'latitude' => $locationData['latitude'] ?? null,
            'longitude' => $locationData['longitude'] ?? null,
        ];
    }

    /**
     * Get client IP address.
     */
    protected function getClientIpAddress(Request $request): string
    {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];

        foreach ($ipKeys as $key) {
            if ($request->server($key)) {
                $ips = explode(',', $request->server($key));
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $request->ip();
    }

    /**
     * Generate device fingerprint.
     */
    protected function generateDeviceFingerprint(Request $request): string
    {
        $components = [
            $request->userAgent() ?: 'Unknown User Agent',
            $request->header('Accept-Language') ?: 'en-US',
            $request->header('Accept-Encoding') ?: 'gzip, deflate',
            $this->agent->platform() ?: 'Unknown Platform',
            $this->agent->browser() ?: 'Unknown Browser',
        ];

        return hash('sha256', implode('|', array_filter($components)));
    }

    /**
     * Get device type.
     */
    protected function getDeviceType(): string
    {
        if ($this->agent->isMobile()) {
            return 'mobile';
        } elseif ($this->agent->isTablet()) {
            return 'tablet';
        } elseif ($this->agent->isDesktop()) {
            return 'desktop';
        }

        return 'unknown';
    }

    /**
     * Get device name.
     */
    protected function getDeviceName(): ?string
    {
        $device = $this->agent->device();
        return $device !== false ? $device : null;
    }

    /**
     * Get location data from IP address.
     */
    protected function getLocationData(string $ipAddress): array
    {
        // Skip location lookup for local/private IPs
        if (!filter_var($ipAddress, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
            return [];
        }

        try {
            // Using a free IP geolocation service (you can replace with your preferred service)
            $response = Http::timeout(5)->get("http://ip-api.com/json/{$ipAddress}");
            
            if ($response->successful()) {
                $data = $response->json();
                
                if ($data['status'] === 'success') {
                    return [
                        'country' => $data['country'] ?? null,
                        'country_code' => $data['countryCode'] ?? null,
                        'region' => $data['regionName'] ?? null,
                        'city' => $data['city'] ?? null,
                        'timezone' => $data['timezone'] ?? null,
                        'latitude' => $data['lat'] ?? null,
                        'longitude' => $data['lon'] ?? null,
                    ];
                }
            }
        } catch (\Exception $e) {
            // Log error but don't fail the login process
            \Log::warning('Failed to get location data for IP: ' . $ipAddress, ['error' => $e->getMessage()]);
        }

        return [];
    }

    /**
     * Check if this is a new device for the user.
     */
    protected function isNewDevice(User $user, string $deviceFingerprint): bool
    {
        return !LoginHistory::where('user_id', $user->id)
            ->where('device_fingerprint', $deviceFingerprint)
            ->where('login_successful', true)
            ->where('is_deleted', false)
            ->exists();
    }

    /**
     * Check if this is a new location for the user.
     */
    protected function isNewLocation(User $user, string $ipAddress): bool
    {
        return !LoginHistory::where('user_id', $user->id)
            ->where('ip_address', $ipAddress)
            ->where('login_successful', true)
            ->where('is_deleted', false)
            ->exists();
    }

    /**
     * Determine if login is suspicious.
     */
    protected function isSuspiciousLogin(User $user, array $loginData): bool
    {
        $suspiciousFactors = 0;

        // Check for new device
        if ($this->isNewDevice($user, $loginData['device_fingerprint'])) {
            $suspiciousFactors++;
        }

        // Check for new location
        if ($this->isNewLocation($user, $loginData['ip_address'])) {
            $suspiciousFactors++;
        }

        // Check for unusual time (outside normal hours)
        $hour = now()->hour;
        if ($hour < 6 || $hour > 23) {
            $suspiciousFactors++;
        }

        // Check for multiple recent failed attempts
        $recentFailedAttempts = LoginHistory::where('user_id', $user->id)
            ->where('login_successful', false)
            ->where('login_at', '>=', now()->subHours(1))
            ->count();

        if ($recentFailedAttempts >= 3) {
            $suspiciousFactors++;
        }

        // Consider suspicious if 2 or more factors
        return $suspiciousFactors >= 2;
    }

    /**
     * Gather additional data for extensibility.
     */
    protected function gatherAdditionalData(Request $request): array
    {
        return [
            'referer' => $request->header('Referer'),
            'accept_language' => $request->header('Accept-Language'),
            'accept_encoding' => $request->header('Accept-Encoding'),
            'connection' => $request->header('Connection'),
            'screen_resolution' => $request->input('screen_resolution'), // Can be sent via JS
            'timezone_offset' => $request->input('timezone_offset'), // Can be sent via JS
        ];
    }

    /**
     * Get login statistics for a user.
     */
    public function getLoginStats(User $user, int $days = 30): array
    {
        $startDate = now()->subDays($days);
        
        $totalLogins = $user->loginHistories()
            ->where('login_at', '>=', $startDate)
            ->where('is_deleted', false)
            ->count();

        $successfulLogins = $user->loginHistories()
            ->where('login_at', '>=', $startDate)
            ->where('login_successful', true)
            ->where('is_deleted', false)
            ->count();

        $failedLogins = $user->loginHistories()
            ->where('login_at', '>=', $startDate)
            ->where('login_successful', false)
            ->where('is_deleted', false)
            ->count();

        $uniqueDevices = $user->loginHistories()
            ->where('login_at', '>=', $startDate)
            ->where('login_successful', true)
            ->where('is_deleted', false)
            ->distinct('device_fingerprint')
            ->count();

        $uniqueLocations = $user->loginHistories()
            ->where('login_at', '>=', $startDate)
            ->where('login_successful', true)
            ->where('is_deleted', false)
            ->distinct('ip_address')
            ->count();

        // Session duration statistics
        $sessionsWithDuration = $user->loginHistories()
            ->where('login_at', '>=', $startDate)
            ->where('login_successful', true)
            ->whereNotNull('session_duration')
            ->where('is_deleted', false);

        $totalSessionDuration = $sessionsWithDuration->sum('session_duration');
        $sessionCount = $sessionsWithDuration->count();
        $averageSessionDuration = $sessionCount > 0 ? round($totalSessionDuration / $sessionCount) : 0;

        // Get longest and shortest sessions
        $longestSession = $sessionsWithDuration->max('session_duration') ?? 0;
        $shortestSession = $sessionsWithDuration->min('session_duration') ?? 0;

        // Active sessions (logged in but not logged out)
        $activeSessions = $user->loginHistories()
            ->where('login_at', '>=', $startDate)
            ->where('login_successful', true)
            ->whereNull('logout_at')
            ->where('is_deleted', false)
            ->count();

        return [
            'total_logins' => $totalLogins,
            'successful_logins' => $successfulLogins,
            'failed_logins' => $failedLogins,
            'success_rate' => $totalLogins > 0 ? round(($successfulLogins / $totalLogins) * 100, 2) : 0,
            'unique_devices' => $uniqueDevices,
            'unique_locations' => $uniqueLocations,
            'total_session_duration' => $totalSessionDuration,
            'average_session_duration' => $averageSessionDuration,
            'average_session_duration_formatted' => $this->formatDuration($averageSessionDuration),
            'longest_session' => $longestSession,
            'longest_session_formatted' => $this->formatDuration($longestSession),
            'shortest_session' => $shortestSession,
            'shortest_session_formatted' => $this->formatDuration($shortestSession),
            'active_sessions' => $activeSessions,
            'sessions_with_duration' => $sessionCount,
        ];
    }

    /**
     * Format duration in seconds to human readable format.
     */
    public function formatDuration(int $seconds): string
    {
        if ($seconds < 60) {
            return $seconds . ' seconds';
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            $remainingSeconds = $seconds % 60;
            return $minutes . ' minutes' . ($remainingSeconds > 0 ? ' ' . $remainingSeconds . ' seconds' : '');
        } else {
            $hours = floor($seconds / 3600);
            $remainingMinutes = floor(($seconds % 3600) / 60);
            $remainingSeconds = $seconds % 60;

            $formatted = $hours . ' hours';
            if ($remainingMinutes > 0) {
                $formatted .= ' ' . $remainingMinutes . ' minutes';
            }
            if ($remainingSeconds > 0) {
                $formatted .= ' ' . $remainingSeconds . ' seconds';
            }

            return $formatted;
        }
    }

    /**
     * Get global login statistics for admin dashboard.
     */
    public function getGlobalLoginStats(int $days = 30): array
    {
        $startDate = now()->subDays($days);

        $totalLogins = LoginHistory::where('login_at', '>=', $startDate)
            ->where('is_deleted', false)
            ->count();

        $successfulLogins = LoginHistory::where('login_at', '>=', $startDate)
            ->where('login_successful', true)
            ->where('is_deleted', false)
            ->count();

        $failedLogins = LoginHistory::where('login_at', '>=', $startDate)
            ->where('login_successful', false)
            ->where('is_deleted', false)
            ->count();

        $uniqueUsers = LoginHistory::where('login_at', '>=', $startDate)
            ->where('login_successful', true)
            ->where('is_deleted', false)
            ->distinct('user_id')
            ->count();

        // Global session duration statistics
        $sessionsWithDuration = LoginHistory::where('login_at', '>=', $startDate)
            ->where('login_successful', true)
            ->whereNotNull('session_duration')
            ->where('is_deleted', false);

        $totalSessionDuration = $sessionsWithDuration->sum('session_duration');
        $sessionCount = $sessionsWithDuration->count();
        $averageSessionDuration = $sessionCount > 0 ? round($totalSessionDuration / $sessionCount) : 0;

        // Active sessions globally
        $activeSessions = LoginHistory::where('login_at', '>=', $startDate)
            ->where('login_successful', true)
            ->whereNull('logout_at')
            ->where('is_deleted', false)
            ->count();

        return [
            'total_logins' => $totalLogins,
            'successful_logins' => $successfulLogins,
            'failed_logins' => $failedLogins,
            'success_rate' => $totalLogins > 0 ? round(($successfulLogins / $totalLogins) * 100, 2) : 0,
            'unique_users' => $uniqueUsers,
            'total_session_duration' => $totalSessionDuration,
            'average_session_duration' => $averageSessionDuration,
            'average_session_duration_formatted' => $this->formatDuration($averageSessionDuration),
            'active_sessions' => $activeSessions,
            'sessions_with_duration' => $sessionCount,
        ];
    }
}
