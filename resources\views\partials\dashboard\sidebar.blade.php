{{-- Dashboard Sidebar Navigation --}}
@php
use Illuminate\Support\Facades\Storage;
@endphp
<aside class="fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-neutral-200 transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 shadow-lg flex flex-col" id="dashboard-sidebar">
    {{-- Logo Section --}}
    <div class="flex items-center justify-center h-16 px-6 bg-gradient-to-r from-primary-800 to-primary-700 border-b border-primary-600">
        <a href="{{ auth()->user()->isAdminOrStaff() ? route('admin.dashboard') : route('dashboard') }}" class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 2L3 7v11a1 1 0 001 1h3v-7h6v7h3a1 1 0 001-1V7l-7-5z"/>
                </svg>
            </div>
            <span class="text-lg font-semibold text-white">ChiSolution</span>
        </a>
    </div>

    {{-- User Profile Section --}}
    <div class="p-6 bg-gradient-to-br from-neutral-50 to-primary-50 border-b border-neutral-200">
        <div class="flex items-center space-x-3">
            @if(auth()->user()->avatar)
                <img class="w-10 h-10 rounded-full object-cover border-2 border-gray-200 shadow-md"
                     src="{{ Storage::url(auth()->user()->avatar) }}"
                     alt="{{ auth()->user()->full_name }}">
            @else
                <div class="w-10 h-10 bg-gradient-to-br from-primary-600 to-primary-700 rounded-full flex items-center justify-center shadow-md">
                    <span class="text-sm font-medium text-white">
                        {{ substr(auth()->user()->first_name, 0, 1) }}{{ substr(auth()->user()->last_name, 0, 1) }}
                    </span>
                </div>
            @endif
            <div class="flex-1 min-w-0">
                <p class="text-sm font-semibold text-primary-900 truncate">
                    {{ auth()->user()->first_name }} {{ auth()->user()->last_name }}
                </p>
                <p class="text-xs text-primary-600 truncate">
                    {{ ucfirst(auth()->user()->role->name ?? 'customer') }}
                </p>
            </div>
        </div>
    </div>

    {{-- Navigation Menu --}}
    <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto scrollbar-hide min-h-0">
        {{-- Dashboard Home --}}
        <a href="{{ route('dashboard') }}"
           class="nav-item {{ request()->routeIs('dashboard') ? 'nav-item-active' : '' }}">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"/>
            </svg>
            <span>{{ __('common.dashboard') }}</span>
        </a>

        {{-- ADMIN NAVIGATION - MOVED TO partials/admin/sidebar.blade.php --}}
        {{--
        @if(auth()->user()->isAdminOrStaff())
            {{-- Admin Navigation --}}
            {{-- <div class="pt-4">
                <h3 class="px-3 text-xs font-semibold text-primary-500 uppercase tracking-wider">Ecommerce</h3>
                <div class="mt-2 space-y-1">
                    <a href="{{ route('admin.categories.index') }}"
                       class="nav-item {{ request()->routeIs('admin.categories.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                        <span>Categories</span>
                    </a>

                    <a href="{{ route('admin.products.index') }}"
                       class="nav-item {{ request()->routeIs('admin.products.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                        </svg>
                        <span>Products</span>
                    </a>

                    <a href="{{ route('admin.orders.index') }}"
                       class="nav-item {{ request()->routeIs('admin.orders.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        <span>Orders</span>
                    </a>

                    <a href="{{ route('admin.coupons.index') }}"
                       class="nav-item {{ request()->routeIs('admin.coupons.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                        </svg>
                        <span>Coupons</span>
                    </a>
                </div>
            </div>

            <div class="pt-4">
                <h3 class="px-3 text-xs font-semibold text-primary-500 uppercase tracking-wider">Projects</h3>
                <div class="mt-2 space-y-1">
                    <a href="{{ route('admin.projects.index') }}"
                       class="nav-item {{ request()->routeIs('admin.projects.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                        <span>Manage Projects</span>
                    </a>

                    <a href="{{ route('my-projects.index') }}"
                       class="nav-item {{ request()->routeIs('my-projects.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                        </svg>
                        <span>View All Projects</span>
                    </a>
                </div>
            </div> --}}
        {{-- END ADMIN NAVIGATION --}}

            {{-- ADMIN MARKETING MANAGEMENT - MOVED TO partials/admin/sidebar.blade.php --}}
            {{-- <div class="pt-4">
                <h3 class="px-3 text-xs font-semibold text-primary-500 uppercase tracking-wider">Marketing Management</h3>
                <div class="mt-2 space-y-1">
                    <a href="{{ route('admin.email-campaigns.index') }}"
                       class="nav-item {{ request()->routeIs('admin.email-campaigns.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                        </svg>
                        <span>Email Campaigns</span>
                    </a>

                    <a href="{{ route('admin.email-templates.index') }}"
                       class="nav-item {{ request()->routeIs('admin.email-templates.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        <span>Email Templates</span>
                    </a>

                    <a href="{{ route('admin.newsletter-subscriptions.index') }}"
                       class="nav-item {{ request()->routeIs('admin.newsletter-subscriptions.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                        <span>Newsletter Subscriptions</span>
                    </a>

                    <a href="{{ route('admin.subscriber-tags.index') }}"
                       class="nav-item {{ request()->routeIs('admin.subscriber-tags.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                        </svg>
                        <span>Subscriber Tags</span>
                    </a>

                    <a href="{{ route('admin.email-analytics.index') }}"
                       class="nav-item {{ request()->routeIs('admin.email-analytics.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                        <span>Email Analytics</span>
                    </a>
                </div>
            </div> --}}

            <div class="pt-4">
                <h3 class="px-3 text-xs font-semibold text-primary-500 uppercase tracking-wider">Management</h3>
                <div class="mt-2 space-y-1">
                    <a href="{{ route('admin.users.index') }}"
                       class="nav-item {{ request()->routeIs('admin.users.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                        </svg>
                        <span>Users</span>
                    </a>

                    @if(auth()->user()->isAdmin())
                        <a href="{{ route('admin.permissions.index') }}"
                           class="nav-item {{ request()->routeIs('admin.permissions.*') ? 'nav-item-active' : '' }}">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                            </svg>
                            <span>Permissions</span>
                        </a>
                    @endif

                    <a href="{{ route('admin.project-applications.index') }}"
                       class="nav-item {{ request()->routeIs('admin.project-applications.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
                        </svg>
                        <span>Project Applications</span>
                    </a>

                    <a href="{{ route('admin.jobs.index') }}"
                       class="nav-item {{ request()->routeIs('admin.jobs.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 8v10a2 2 0 002 2h4a2 2 0 002-2V8M8 8V6a2 2 0 012-2h4a2 2 0 012-2V8"/>
                        </svg>
                        <span>Job Management</span>
                    </a>

                    <a href="{{ route('admin.job-applications.index') }}"
                       class="nav-item {{ request()->routeIs('admin.job-applications.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                        <span>Job Applications</span>
                    </a>

                    <a href="{{ route('admin.contact-submissions.index') }}"
                       class="nav-item {{ request()->routeIs('admin.contact-submissions.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3"></path>
                        </svg>
                        <span>Contact Submissions</span>
                    </a>

                    <a href="{{ route('admin.visitor-analytics.index') }}"
                       class="nav-item {{ request()->routeIs('admin.visitor-analytics.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <span>Visitor Analytics</span>
                    </a>

                    <a href="{{ route('admin.activity-logs.index') }}"
                       class="nav-item {{ request()->routeIs('admin.activity-logs.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        <span>Activity Logs</span>
                    </a>

                    <a href="{{ route('admin.chat.moderation.index') }}"
                       class="nav-item {{ request()->routeIs('admin.chat.moderation.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                        </svg>
                        <span>Chat Moderation</span>
                    </a>

                    <a href="{{ route('admin.chat.ai.index') }}"
                       class="nav-item {{ request()->routeIs('admin.chat.ai.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                        <span>AI Configuration</span>
                    </a>
                </div>
            </div>

            <div class="pt-4">
                <h3 class="px-3 text-xs font-semibold text-primary-500 uppercase tracking-wider">Tools</h3>
                <div class="mt-2 space-y-1">
                    <a href="{{ route('admin.search.advanced') }}"
                       class="nav-item {{ request()->routeIs('admin.search.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        <span>Advanced Search</span>
                    </a>
                </div>
            </div>
        @elseif(auth()->user()->isCustomer())
            {{-- Customer Navigation --}}
            <div class="pt-4">
                <h3 class="px-3 text-xs font-semibold text-primary-500 uppercase tracking-wider">Projects</h3>
                <div class="mt-2 space-y-1">
                    <a href="{{ route('project-applications.create') }}"
                       class="nav-item {{ request()->routeIs('project-applications.create') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        <span>New Application</span>
                    </a>

                    <a href="{{ route('project-applications.index') }}"
                       class="nav-item {{ request()->routeIs('project-applications.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
                        </svg>
                        <span>My Applications</span>
                    </a>

                    <a href="{{ route('my-projects.index') }}"
                       class="nav-item {{ request()->routeIs('my-projects.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                        <span>My Projects</span>
                    </a>
                </div>
            </div>

            <div class="pt-4">
                <h3 class="px-3 text-xs font-semibold text-primary-500 uppercase tracking-wider">Careers</h3>
                <div class="mt-2 space-y-1">
                    <a href="{{ route('careers.index') }}"
                       class="nav-item {{ request()->routeIs('careers.index') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 8v10a2 2 0 002 2h4a2 2 0 002-2V8M8 8V6a2 2 0 012-2h4a2 2 0 012-2V8"/>
                        </svg>
                        <span>Browse Jobs</span>
                    </a>

                    <a href="{{ route('careers.my-applications') }}"
                       class="nav-item {{ request()->routeIs('careers.my-applications') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
                        </svg>
                        <span>My Job Applications</span>
                    </a>
                </div>
            </div>

            <div class="pt-4">
                <h3 class="px-3 text-xs font-semibold text-primary-500 uppercase tracking-wider">Shopping</h3>
                <div class="mt-2 space-y-1">
                    <a href="{{ route('shop.index') }}"
                       class="nav-item {{ request()->routeIs('shop.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                        </svg>
                        <span>Browse Products</span>
                    </a>

                    <a href="{{ route('cart.index') }}"
                       class="nav-item {{ request()->routeIs('cart.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5"/>
                        </svg>
                        <span>{{ __('common.cart') }}</span>
                        @if($globalCartCount > 0)
                            <span class="ml-auto bg-secondary-600 text-white text-xs rounded-full px-2 py-1 animate-pulse">
                                {{ $globalCartCount }}
                            </span>
                        @endif
                    </a>

                    <a href="{{ route('orders.index') }}"
                       class="nav-item {{ request()->routeIs('orders.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        <span>{{ __('common.my_orders') }}</span>
                    </a>
                </div>
            </div>
        @else
            {{-- Client Navigation --}}
            <div class="pt-4">
                <h3 class="px-3 text-xs font-semibold text-primary-500 uppercase tracking-wider">Projects</h3>
                <div class="mt-2 space-y-1">
                    <a href="{{ route('project-applications.create') }}"
                       class="nav-item {{ request()->routeIs('project-applications.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        <span>New Application</span>
                    </a>

                    <a href="{{ route('project-applications.index') }}"
                       class="nav-item {{ request()->routeIs('project-applications.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
                        </svg>
                        <span>My Applications</span>
                    </a>

                    <a href="{{ route('my-projects.index') }}"
                       class="nav-item {{ request()->routeIs('my-projects.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                        <span>My Projects</span>
                    </a>

                    <a href="{{ route('orders.index') }}"
                       class="nav-item {{ request()->routeIs('orders.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        <span>Orders</span>
                    </a>
                </div>
            </div>

            <div class="pt-4">
                <h3 class="px-3 text-xs font-semibold text-primary-500 uppercase tracking-wider">Careers</h3>
                <div class="mt-2 space-y-1">
                    <a href="{{ route('careers.index') }}"
                       class="nav-item {{ request()->routeIs('careers.index') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 8v10a2 2 0 002 2h4a2 2 0 002-2V8M8 8V6a2 2 0 012-2h4a2 2 0 012-2V8"/>
                        </svg>
                        <span>Browse Jobs</span>
                    </a>

                    <a href="{{ route('careers.my-applications') }}"
                       class="nav-item {{ request()->routeIs('careers.my-applications') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
                        </svg>
                        <span>My Job Applications</span>
                    </a>
                </div>
            </div>

            <div class="pt-4">
                <h3 class="px-3 text-xs font-semibold text-primary-500 uppercase tracking-wider">Shopping</h3>
                <div class="mt-2 space-y-1">
                    <a href="{{ route('shop.index') }}" 
                       class="nav-item {{ request()->routeIs('shop.*') ? 'nav-item-active' : '' }}">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                        </svg>
                        <span>Browse Products</span>
                    </a>
                </div>
            </div>
        @endif

        {{-- Account Section --}}
        <div class="pt-4">
            <h3 class="px-3 text-xs font-semibold text-primary-500 uppercase tracking-wider">Account</h3>
            <div class="mt-2 space-y-1">
                <a href="{{ route('profile.edit') }}"
                   class="nav-item {{ request()->routeIs('profile.*') ? 'nav-item-active' : '' }}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                    <span>Profile Settings</span>
                </a>

                <a href="{{ route('admin.preferences.index') }}"
                   class="nav-item {{ request()->routeIs('admin.preferences.*') ? 'nav-item-active' : '' }}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <span>User Preferences</span>
                </a>

                <a href="{{ route('home', ['locale' => app()->getLocale()]) }}"
                   class="nav-item">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                    </svg>
                    <span>Back to Website</span>
                </a>
            </div>
        </div>
    </nav>

    {{-- Logout Section --}}
    <div class="p-4 border-t border-neutral-200">
        <form method="POST" action="{{ route('logout') }}">
            @csrf
            <button type="submit" class="w-full flex items-center px-3 py-2 text-sm font-medium text-danger-600 rounded-lg hover:bg-danger-50 hover:text-danger-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                </svg>
                <span>Sign Out</span>
            </button>
        </form>
    </div>
</aside>

{{-- Sidebar Styles --}}
<style>
.nav-item {
    @apply flex items-center px-3 py-2.5 text-sm font-medium text-neutral-700 rounded-lg hover:bg-gradient-to-r hover:from-primary-50 hover:to-secondary-50 hover:text-primary-800 transition-all duration-200 group;
}

.nav-item-active {
    @apply bg-gradient-to-r from-primary-100 to-secondary-100 text-primary-900 border-l-4 border-secondary-600 shadow-sm;
}

.nav-item svg {
    @apply mr-3 flex-shrink-0 group-hover:text-primary-700;
}

.nav-item-active svg {
    @apply text-primary-800;
}
</style>
