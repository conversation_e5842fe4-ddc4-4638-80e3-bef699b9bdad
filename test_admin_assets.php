<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Admin Asset Loading...\n\n";

try {
    // Get or create admin user
    $adminRole = Role::where('name', 'admin')->first();
    if (!$adminRole) {
        echo "❌ Admin role not found. Running seeders...\n";
        \Artisan::call('db:seed', ['--class' => 'RoleSeeder']);
        $adminRole = Role::where('name', 'admin')->first();
    }
    
    $adminUser = User::where('email', '<EMAIL>')->first();
    if (!$adminUser) {
        $adminUser = User::create([
            'uuid' => \Str::uuid(),
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role_id' => $adminRole->id,
            'is_active' => true,
            'is_deleted' => false,
        ]);
    }
    
    echo "✅ Admin user ready (ID: {$adminUser->id})\n\n";

    // Test admin pages
    $adminPagesToTest = [
        ['/admin/dashboard', 'Admin Dashboard (admin.css)'],
        ['/admin/settings', 'Admin Settings (admin.css)'],
        ['/admin/services', 'Admin Services (admin.css)'],
        ['/admin/login-history', 'Admin Login History (admin.css)'],
    ];

    foreach ($adminPagesToTest as [$url, $description]) {
        echo "Testing: {$description}\n";
        echo "URL: {$url}\n";
        
        try {
            $request = Request::create($url, 'GET');
            
            // Authenticate as admin
            $request->setUserResolver(function () use ($adminUser) {
                return $adminUser;
            });
            
            $response = $app->handle($request);
            $content = $response->getContent();
            
            // Check if Vite assets are being loaded
            $hasViteAssets = strpos($content, '@vite') !== false || 
                           strpos($content, '/build/assets/') !== false ||
                           strpos($content, 'vite') !== false;
            
            // Check for specific asset files
            $hasAdminCSS = strpos($content, 'admin.css') !== false || strpos($content, 'admin-') !== false;
            $hasAppJS = strpos($content, 'app.js') !== false || strpos($content, 'app-') !== false;
            
            echo "  Status: {$response->getStatusCode()}\n";
            echo "  Content Length: " . strlen($content) . " bytes\n";
            echo "  Has Vite Assets: " . ($hasViteAssets ? 'Yes' : 'No') . "\n";
            echo "  Has admin.css: " . ($hasAdminCSS ? 'Yes' : 'No') . "\n";
            echo "  Has app.js: " . ($hasAppJS ? 'Yes' : 'No') . "\n";
            
            // Extract and show asset references
            if (preg_match_all('/\/build\/assets\/[^"\']+/', $content, $matches)) {
                echo "  Asset Files Found:\n";
                foreach (array_unique($matches[0]) as $asset) {
                    echo "    - {$asset}\n";
                }
            }
            
            // Check for admin layout elements
            $hasSidebar = strpos($content, 'sidebar') !== false;
            $hasAdminHeader = strpos($content, 'admin') !== false && strpos($content, 'header') !== false;
            $hasNavigation = strpos($content, 'nav') !== false;
            
            echo "  Has Sidebar: " . ($hasSidebar ? 'Yes' : 'No') . "\n";
            echo "  Has Admin Header: " . ($hasAdminHeader ? 'Yes' : 'No') . "\n";
            echo "  Has Navigation: " . ($hasNavigation ? 'Yes' : 'No') . "\n";
            
            // Check for Tailwind classes
            $hasTailwindClasses = preg_match('/class="[^"]*(?:bg-|text-|p-|m-|flex|grid)/', $content);
            echo "  Has Tailwind Classes: " . ($hasTailwindClasses ? 'Yes' : 'No') . "\n";
            
            // Check if it's a redirect (302)
            if ($response->getStatusCode() === 302) {
                echo "  Redirect Location: " . $response->headers->get('Location') . "\n";
            }
            
        } catch (Exception $e) {
            echo "  ERROR: " . $e->getMessage() . "\n";
        }
        
        echo "\n" . str_repeat("-", 60) . "\n\n";
    }

    // Test client dashboard
    echo "Testing Client Dashboard:\n";
    echo "URL: /dashboard\n";
    
    try {
        $request = Request::create('/dashboard', 'GET');
        
        // Authenticate as admin (who can also access client dashboard)
        $request->setUserResolver(function () use ($adminUser) {
            return $adminUser;
        });
        
        $response = $app->handle($request);
        $content = $response->getContent();
        
        $hasClientCSS = strpos($content, 'client.css') !== false || strpos($content, 'client-') !== false;
        
        echo "  Status: {$response->getStatusCode()}\n";
        echo "  Content Length: " . strlen($content) . " bytes\n";
        echo "  Has client.css: " . ($hasClientCSS ? 'Yes' : 'No') . "\n";
        
        if (preg_match_all('/\/build\/assets\/[^"\']+/', $content, $matches)) {
            echo "  Asset Files Found:\n";
            foreach (array_unique($matches[0]) as $asset) {
                echo "    - {$asset}\n";
            }
        }
        
    } catch (Exception $e) {
        echo "  ERROR: " . $e->getMessage() . "\n";
    }

} catch (Exception $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
