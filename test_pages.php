<?php

/**
 * Simple test script to verify that key pages are accessible
 * This script tests the routes without requiring authentication
 */

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Test routes
$routes_to_test = [
    '/' => 'Home page redirect',
    '/en' => 'English home page',
    '/en/about' => 'About page',
    '/en/services' => 'Services page',
    '/en/contact' => 'Contact page',
];

echo "Testing ChiSolution Pages\n";
echo "========================\n\n";

foreach ($routes_to_test as $route => $description) {
    try {
        $request = Request::create($route, 'GET');
        $response = $kernel->handle($request);
        
        $status = $response->getStatusCode();
        $statusText = $status == 200 ? 'OK' : 
                     ($status >= 300 && $status < 400 ? 'REDIRECT' : 'ERROR');
        
        echo sprintf("%-30s | %-15s | %d %s\n", 
            $description, 
            $route, 
            $status, 
            $statusText
        );
        
        // Clean up
        $kernel->terminate($request, $response);
        
    } catch (Exception $e) {
        echo sprintf("%-30s | %-15s | ERROR: %s\n", 
            $description, 
            $route, 
            $e->getMessage()
        );
    }
}

echo "\n";
echo "Route Testing Complete\n";
echo "======================\n";

// Test if key services are available
echo "\nTesting Key Services:\n";
echo "--------------------\n";

try {
    $imageService = $app->make(\App\Services\ImageService::class);
    echo "✓ ImageService: Available\n";
} catch (Exception $e) {
    echo "✗ ImageService: " . $e->getMessage() . "\n";
}

try {
    $fileService = $app->make(\App\Services\FileService::class);
    echo "✓ FileService: Available\n";
} catch (Exception $e) {
    echo "✗ FileService: " . $e->getMessage() . "\n";
}

try {
    $loginHistoryService = $app->make(\App\Services\LoginHistoryService::class);
    echo "✓ LoginHistoryService: Available\n";
} catch (Exception $e) {
    echo "✗ LoginHistoryService: " . $e->getMessage() . "\n";
}

try {
    $activityLogger = $app->make(\App\Services\ActivityLogger::class);
    echo "✓ ActivityLogger: Available\n";
} catch (Exception $e) {
    echo "✗ ActivityLogger: " . $e->getMessage() . "\n";
}

echo "\nService Testing Complete\n";
