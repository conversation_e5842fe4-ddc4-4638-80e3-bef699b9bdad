<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 COMPREHENSIVE UI & ASSET TESTING\n";
echo "=====================================\n\n";

try {
    // Test 1: Asset Compilation Status
    echo "1. ASSET COMPILATION STATUS\n";
    echo "----------------------------\n";
    
    $assetFiles = [
        'public/build/assets/app-JlwmLI3t.css' => 'App CSS',
        'public/build/assets/frontend-2-Ul6gor.css' => 'Frontend CSS',
        'public/build/assets/admin-Pt-CQbui.css' => 'Admin CSS',
        'public/build/assets/client-CPxHxGl5.css' => 'Client CSS',
        'public/build/assets/app-CCLt7DvH.js' => 'App JS',
        'public/build/manifest.json' => 'Vite Manifest'
    ];
    
    $allAssetsExist = true;
    foreach ($assetFiles as $file => $description) {
        $exists = file_exists($file);
        $size = $exists ? filesize($file) : 0;
        $status = $exists ? "✅ EXISTS ({$size} bytes)" : "❌ MISSING";
        echo "  {$description}: {$status}\n";
        if (!$exists) $allAssetsExist = false;
    }
    
    echo "\n📊 Asset Compilation: " . ($allAssetsExist ? "✅ ALL ASSETS COMPILED" : "❌ MISSING ASSETS") . "\n\n";

    // Test 2: Frontend Pages Asset Loading
    echo "2. FRONTEND PAGES ASSET LOADING\n";
    echo "--------------------------------\n";
    
    $frontendPages = [
        ['/en/', 'Homepage', 'frontend.css'],
        ['/login', 'Login Page', 'app.css'],
        ['/register', 'Register Page', 'app.css'],
        ['/en/about', 'About Page', 'frontend.css'],
        ['/en/contact', 'Contact Page', 'frontend.css'],
        ['/en/services', 'Services Page', 'frontend.css'],
    ];

    $frontendSuccess = 0;
    foreach ($frontendPages as [$url, $description, $expectedCSS]) {
        try {
            $request = Request::create($url, 'GET');
            $response = $app->handle($request);
            $content = $response->getContent();
            
            $hasExpectedCSS = strpos($content, $expectedCSS) !== false || 
                            strpos($content, str_replace('.css', '-', $expectedCSS)) !== false;
            $hasViteAssets = strpos($content, '/build/assets/') !== false;
            $hasTailwindClasses = preg_match('/class="[^"]*(?:bg-|text-|p-|m-|flex|grid)/', $content);
            
            $status = ($response->getStatusCode() === 200 && $hasExpectedCSS && $hasViteAssets && $hasTailwindClasses) ? "✅" : "❌";
            
            echo "  {$description} ({$url}): {$status}\n";
            echo "    Status: {$response->getStatusCode()} | CSS: " . ($hasExpectedCSS ? "✅" : "❌") . " | Vite: " . ($hasViteAssets ? "✅" : "❌") . " | Tailwind: " . ($hasTailwindClasses ? "✅" : "❌") . "\n";
            
            if ($status === "✅") $frontendSuccess++;
            
        } catch (Exception $e) {
            echo "  {$description} ({$url}): ❌ ERROR - " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n📊 Frontend Pages: {$frontendSuccess}/" . count($frontendPages) . " working correctly\n\n";

    // Test 3: Sidebar Navigation Analysis
    echo "3. ADMIN SIDEBAR NAVIGATION ANALYSIS\n";
    echo "-------------------------------------\n";
    
    $sidebarFile = 'resources/views/partials/admin/sidebar.blade.php';
    if (file_exists($sidebarFile)) {
        $sidebarContent = file_get_contents($sidebarFile);
        
        // Count navigation items
        $navItems = preg_match_all('/route\(\'admin\.([^\']+)\.index\'\)/', $sidebarContent, $matches);
        $dropdownSections = preg_match_all('/x-data="\{ open: false \}"/', $sidebarContent);
        $hasAlpineJS = strpos($sidebarContent, 'x-data') !== false;
        
        echo "  📋 Navigation Items Found: {$navItems}\n";
        echo "  📁 Dropdown Sections: {$dropdownSections}\n";
        echo "  🎛️  Alpine.js Integration: " . ($hasAlpineJS ? "✅ YES" : "❌ NO") . "\n";
        
        // Check for specific sections
        $expectedSections = [
            'dashboard' => 'Dashboard',
            'users' => 'Users Management',
            'projects' => 'Projects Management',
            'services' => 'Services Management',
            'orders' => 'Orders Management',
            'login-history' => 'Login History',
            'activity-logs' => 'Activity Logs',
            'email-campaigns' => 'Email Campaigns',
            'categories' => 'Categories Management',
            'products' => 'Products Management',
            'coupons' => 'Coupons Management',
            'jobs' => 'Jobs Management',
            'job-applications' => 'Job Applications',
            'project-applications' => 'Project Applications',
            'contact-submissions' => 'Contact Submissions',
            'comments' => 'Comments Management',
            'chat' => 'Chat Management',
            'email' => 'Email Management',
            'visitor-analytics' => 'Visitor Analytics',
            'permissions' => 'Permissions Management',
            'ai-providers' => 'AI Providers',
            'settings' => 'Settings'
        ];
        
        echo "\n  🔍 Navigation Sections Analysis:\n";
        $sectionsFound = 0;
        foreach ($expectedSections as $route => $name) {
            $found = strpos($sidebarContent, "admin.{$route}") !== false || 
                    strpos($sidebarContent, $route) !== false;
            echo "    {$name}: " . ($found ? "✅" : "❌") . "\n";
            if ($found) $sectionsFound++;
        }
        
        echo "\n📊 Sidebar Sections: {$sectionsFound}/" . count($expectedSections) . " sections present\n";
        
    } else {
        echo "  ❌ Sidebar file not found!\n";
    }
    
    echo "\n";

    // Test 4: Admin Layout Alpine.js Integration
    echo "4. ADMIN LAYOUT ALPINE.JS INTEGRATION\n";
    echo "--------------------------------------\n";
    
    $adminLayoutFile = 'resources/views/layouts/admin.blade.php';
    if (file_exists($adminLayoutFile)) {
        $adminLayoutContent = file_get_contents($adminLayoutFile);
        $hasAlpineJS = strpos($adminLayoutContent, 'alpinejs') !== false;
        $hasViteAssets = strpos($adminLayoutContent, '@vite') !== false;
        $hasAdminCSS = strpos($adminLayoutContent, 'admin.css') !== false;
        
        echo "  🎛️  Alpine.js CDN: " . ($hasAlpineJS ? "✅ INCLUDED" : "❌ MISSING") . "\n";
        echo "  📦 Vite Assets: " . ($hasViteAssets ? "✅ INCLUDED" : "❌ MISSING") . "\n";
        echo "  🎨 Admin CSS: " . ($hasAdminCSS ? "✅ INCLUDED" : "❌ MISSING") . "\n";
        
    } else {
        echo "  ❌ Admin layout file not found!\n";
    }
    
    echo "\n";

    // Test 5: CSS Architecture Verification
    echo "5. CSS ARCHITECTURE VERIFICATION\n";
    echo "---------------------------------\n";
    
    $cssFiles = [
        'resources/css/app.css' => 'Comprehensive App CSS',
        'resources/css/frontend.css' => 'Frontend-specific CSS',
        'resources/css/admin.css' => 'Admin-specific CSS',
        'resources/css/client.css' => 'Client dashboard CSS'
    ];
    
    foreach ($cssFiles as $file => $description) {
        if (file_exists($file)) {
            $size = filesize($file);
            $content = file_get_contents($file);
            $hasTailwindImports = strpos($content, '@tailwind') !== false;
            echo "  {$description}: ✅ EXISTS ({$size} bytes) | Tailwind: " . ($hasTailwindImports ? "✅" : "❌") . "\n";
        } else {
            echo "  {$description}: ❌ MISSING\n";
        }
    }

    // Final Summary
    echo "\n";
    echo "🎯 FINAL ASSESSMENT\n";
    echo "===================\n";
    echo "✅ Asset Compilation: " . ($allAssetsExist ? "WORKING" : "ISSUES FOUND") . "\n";
    echo "✅ Frontend Asset Loading: {$frontendSuccess}/" . count($frontendPages) . " pages working\n";
    echo "✅ Admin Sidebar: {$sectionsFound}/" . count($expectedSections) . " sections restored\n";
    echo "✅ Alpine.js Integration: " . (isset($hasAlpineJS) && $hasAlpineJS ? "WORKING" : "NEEDS ATTENTION") . "\n";
    echo "✅ CSS Architecture: Separate files maintained\n\n";
    
    if ($allAssetsExist && $frontendSuccess >= 4 && $sectionsFound >= 15) {
        echo "🎉 SUCCESS: UI and asset issues have been resolved!\n";
        echo "   - Assets are compiling correctly\n";
        echo "   - Frontend pages are loading styles properly\n";
        echo "   - Admin sidebar has been restored with full navigation\n";
        echo "   - Separate CSS architecture is maintained\n";
    } else {
        echo "⚠️  PARTIAL SUCCESS: Some issues may remain\n";
        echo "   - Check individual test results above for details\n";
    }

} catch (Exception $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
