<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 DEBUGGING VITE ASSET REFERENCES\n";
echo "===================================\n\n";

try {
    // Test homepage to see actual asset references
    $request = Request::create('/en/', 'GET');
    $response = $app->handle($request);
    $content = $response->getContent();
    
    echo "📄 HOMEPAGE CONTENT ANALYSIS\n";
    echo "-----------------------------\n";
    echo "Status: {$response->getStatusCode()}\n";
    echo "Content Length: " . strlen($content) . " bytes\n\n";
    
    // Extract all CSS references
    if (preg_match_all('/<link[^>]*href="([^"]*\.css[^"]*)"[^>]*>/', $content, $cssMatches)) {
        echo "🎨 CSS REFERENCES FOUND:\n";
        foreach ($cssMatches[1] as $css) {
            echo "  - {$css}\n";
        }
    } else {
        echo "❌ No CSS references found\n";
    }
    
    echo "\n";
    
    // Extract all JS references
    if (preg_match_all('/<script[^>]*src="([^"]*\.js[^"]*)"[^>]*>/', $content, $jsMatches)) {
        echo "📜 JS REFERENCES FOUND:\n";
        foreach ($jsMatches[1] as $js) {
            echo "  - {$js}\n";
        }
    } else {
        echo "❌ No JS references found\n";
    }
    
    echo "\n";
    
    // Look for @vite directives in the source
    if (strpos($content, '@vite') !== false) {
        echo "🔧 @vite directive found in rendered content (this shouldn't happen)\n";
    } else {
        echo "✅ @vite directives properly processed\n";
    }
    
    // Check for build directory references
    if (preg_match_all('/\/build\/[^"\'>\s]+/', $content, $buildMatches)) {
        echo "\n📦 BUILD DIRECTORY REFERENCES:\n";
        foreach (array_unique($buildMatches[0]) as $build) {
            echo "  - {$build}\n";
        }
    } else {
        echo "\n❌ No /build/ directory references found\n";
    }
    
    // Check for any asset references
    if (preg_match_all('/(?:href|src)="([^"]*(?:css|js)[^"]*)"/', $content, $allAssets)) {
        echo "\n🔗 ALL ASSET REFERENCES:\n";
        foreach (array_unique($allAssets[1]) as $asset) {
            echo "  - {$asset}\n";
        }
    }
    
    // Show a snippet of the head section
    if (preg_match('/<head[^>]*>(.*?)<\/head>/s', $content, $headMatch)) {
        echo "\n📋 HEAD SECTION SNIPPET (first 500 chars):\n";
        echo "----------------------------------------\n";
        echo substr($headMatch[1], 0, 500) . "...\n";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
