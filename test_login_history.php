<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\LoginHistory;
use App\Services\LoginHistoryService;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Testing Login History Functionality\n";
echo "===================================\n\n";

// Test 1: Check if LoginHistoryService exists and can be instantiated
echo "1. Testing LoginHistoryService instantiation...\n";
try {
    $loginHistoryService = app(LoginHistoryService::class);
    echo "✓ LoginHistoryService instantiated successfully\n";
} catch (Exception $e) {
    echo "✗ Failed to instantiate LoginHistoryService: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Check if we can find a test user
echo "\n2. Finding test user...\n";
$user = User::where('email', '<EMAIL>')->first();
if (!$user) {
    $user = User::whereHas('role', function($q) {
        $q->where('name', 'admin');
    })->first();
}
if (!$user) {
    // Try to find any user
    $user = User::first();
}
if (!$user) {
    echo "✗ No users found. Creating test user...\n";
    // Find admin role
    $adminRole = \App\Models\Role::where('name', 'admin')->first();
    if (!$adminRole) {
        $adminRole = \App\Models\Role::create([
            'name' => 'admin',
            'slug' => 'admin',
            'description' => 'Administrator role',
            'is_active' => true,
            'permissions' => ['*']
        ]);
    }

    $user = User::create([
        'first_name' => 'Test',
        'last_name' => 'Admin',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'role_id' => $adminRole->id,
        'email_verified_at' => now(),
        'is_active' => true,
        'uuid' => \Illuminate\Support\Str::uuid(),
    ]);
}
echo "✓ Using user: {$user->email} (ID: {$user->id})\n";

// Test 3: Check current login history count
echo "\n3. Checking current login history count...\n";
$initialCount = LoginHistory::count();
echo "✓ Current login history records: {$initialCount}\n";

// Test 4: Test recording a login
echo "\n4. Testing login history recording...\n";
try {
    // Create a mock request
    $request = Request::create('/login', 'POST', [
        'email' => $user->email,
        'password' => 'password'
    ]);

    // Set some headers to simulate a real request
    $request->headers->set('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    $request->headers->set('Accept-Language', 'en-US,en;q=0.9');
    $request->server->set('REMOTE_ADDR', '127.0.0.1');
    $request->server->set('HTTP_X_FORWARDED_FOR', '*************');

    // Set up session for the request
    $session = app('session.store');
    $session->setId('test-session-' . uniqid());
    $session->start();
    $request->setLaravelSession($session);
    
    // Record the login
    $loginRecord = $loginHistoryService->recordLogin($user, $request, true, 'email');
    
    echo "✓ Login recorded successfully\n";
    echo "  - Record ID: {$loginRecord->id}\n";
    echo "  - Public ID: {$loginRecord->public_id}\n";
    echo "  - IP Address: {$loginRecord->ip_address}\n";
    echo "  - User Agent: " . substr($loginRecord->user_agent, 0, 50) . "...\n";
    echo "  - Location: {$loginRecord->location}\n";
    echo "  - Device Type: {$loginRecord->device_type}\n";
    echo "  - Success: " . ($loginRecord->successful ? 'Yes' : 'No') . "\n";
    
} catch (Exception $e) {
    echo "✗ Failed to record login: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}

// Test 5: Verify the record was saved to database
echo "\n5. Verifying database record...\n";
$newCount = LoginHistory::count();
if ($newCount > $initialCount) {
    echo "✓ New login history record created in database\n";
    echo "  - Total records now: {$newCount}\n";
    echo "  - Records added: " . ($newCount - $initialCount) . "\n";
} else {
    echo "✗ No new record found in database\n";
    exit(1);
}

// Test 6: Test failed login recording
echo "\n6. Testing failed login recording...\n";
try {
    $failedLoginRecord = $loginHistoryService->recordLogin($user, $request, false, 'email', 'Invalid credentials');
    
    echo "✓ Failed login recorded successfully\n";
    echo "  - Record ID: {$failedLoginRecord->id}\n";
    echo "  - Success: " . ($failedLoginRecord->successful ? 'Yes' : 'No') . "\n";
    echo "  - Failure Reason: {$failedLoginRecord->failure_reason}\n";
    
} catch (Exception $e) {
    echo "✗ Failed to record failed login: " . $e->getMessage() . "\n";
}

// Test 7: Check final count
echo "\n7. Final verification...\n";
$finalCount = LoginHistory::count();
echo "✓ Final login history count: {$finalCount}\n";
echo "✓ Total records created in this test: " . ($finalCount - $initialCount) . "\n";

// Test 8: Display recent login history for the user
echo "\n8. Recent login history for user {$user->email}:\n";
$recentLogins = LoginHistory::where('user_id', $user->id)
    ->orderBy('created_at', 'desc')
    ->limit(5)
    ->get();

foreach ($recentLogins as $login) {
    echo "  - " . $login->created_at->format('Y-m-d H:i:s') . " | " 
         . ($login->successful ? 'SUCCESS' : 'FAILED') . " | " 
         . $login->ip_address . " | " 
         . $login->device_type . "\n";
}

echo "\n✓ All tests completed successfully!\n";
echo "✓ Login history functionality is working correctly.\n";
