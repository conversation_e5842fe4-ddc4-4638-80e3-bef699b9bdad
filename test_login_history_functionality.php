<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Role;
use App\Models\LoginHistory;
use App\Services\LoginHistoryService;
use Illuminate\Support\Facades\Hash;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Login History Functionality...\n\n";

try {
    // 1. Check if we have roles
    $adminRole = Role::where('name', 'admin')->first();
    if (!$adminRole) {
        echo "❌ Admin role not found. Running seeders...\n";
        \Artisan::call('db:seed', ['--class' => 'RoleSeeder']);
        $adminRole = Role::where('name', 'admin')->first();
    }
    
    if ($adminRole) {
        echo "✅ Admin role found (ID: {$adminRole->id})\n";
    } else {
        echo "❌ Failed to create admin role\n";
        exit(1);
    }

    // 2. Create or find a test user
    $testUser = User::where('email', '<EMAIL>')->first();
    if (!$testUser) {
        $testUser = User::create([
            'uuid' => \Str::uuid(),
            'first_name' => 'Test',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role_id' => $adminRole->id,
            'is_active' => true,
            'is_deleted' => false,
        ]);
        echo "✅ Created test user (ID: {$testUser->id})\n";
    } else {
        echo "✅ Found existing test user (ID: {$testUser->id})\n";
    }

    // 3. Test LoginHistoryService
    $loginHistoryService = app(LoginHistoryService::class);
    echo "✅ LoginHistoryService instantiated\n";

    // 4. Create a mock request
    $request = Request::create('/login', 'POST', [], [], [], [
        'REMOTE_ADDR' => '127.0.0.1',
        'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'HTTP_X_FORWARDED_FOR' => null,
    ]);

    // 5. Test successful login recording
    echo "\n--- Testing Successful Login Recording ---\n";
    $loginRecord = $loginHistoryService->recordLogin($testUser, $request, true, 'email');
    
    if ($loginRecord && $loginRecord->id) {
        echo "✅ Login history record created successfully\n";
        echo "   - Record ID: {$loginRecord->id}\n";
        echo "   - User ID: {$loginRecord->user_id}\n";
        echo "   - Successful: " . ($loginRecord->login_successful ? 'Yes' : 'No') . "\n";
        echo "   - IP: {$loginRecord->ip_address}\n";
        echo "   - Method: {$loginRecord->login_method}\n";
        echo "   - Device: {$loginRecord->device_type}\n";
        echo "   - Browser: {$loginRecord->browser_name}\n";
        echo "   - Platform: {$loginRecord->platform}\n";
    } else {
        echo "❌ Failed to create login history record\n";
        exit(1);
    }

    // 6. Test failed login recording
    echo "\n--- Testing Failed Login Recording ---\n";
    $failedRecord = $loginHistoryService->recordLogin($testUser, $request, false, 'email', 'Invalid password');
    
    if ($failedRecord && $failedRecord->id) {
        echo "✅ Failed login history record created successfully\n";
        echo "   - Record ID: {$failedRecord->id}\n";
        echo "   - Successful: " . ($failedRecord->login_successful ? 'Yes' : 'No') . "\n";
        echo "   - Failure Reason: {$failedRecord->failure_reason}\n";
    } else {
        echo "❌ Failed to create failed login history record\n";
    }

    // 7. Check database records
    echo "\n--- Database Verification ---\n";
    $totalRecords = LoginHistory::count();
    $userRecords = LoginHistory::where('user_id', $testUser->id)->count();
    $successfulRecords = LoginHistory::where('user_id', $testUser->id)->where('login_successful', true)->count();
    $failedRecords = LoginHistory::where('user_id', $testUser->id)->where('login_successful', false)->count();

    echo "✅ Total login history records: {$totalRecords}\n";
    echo "✅ Records for test user: {$userRecords}\n";
    echo "✅ Successful logins: {$successfulRecords}\n";
    echo "✅ Failed logins: {$failedRecords}\n";

    // 8. Test recent records
    echo "\n--- Recent Login History ---\n";
    $recentRecords = LoginHistory::where('user_id', $testUser->id)
        ->orderBy('login_at', 'desc')
        ->limit(5)
        ->get();

    foreach ($recentRecords as $record) {
        $status = $record->login_successful ? 'success' : 'failed';
        echo "   - {$record->login_at}: {$status} ({$record->login_method})\n";
    }

    echo "\n🎉 Login History functionality is working correctly!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
