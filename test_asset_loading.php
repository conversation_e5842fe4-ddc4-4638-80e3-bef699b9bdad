<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Asset Loading in Blade Templates...\n\n";

try {
    // Test different pages to see if assets are being loaded
    $pagesToTest = [
        ['/', 'Root Redirect'],
        ['/en/', 'Homepage (frontend.css)'],
        ['/login', 'Login Page (app.css)'],
        ['/register', 'Register Page (app.css)'],
    ];

    foreach ($pagesToTest as [$url, $description]) {
        echo "Testing: {$description}\n";
        echo "URL: {$url}\n";
        
        try {
            $request = Request::create($url, 'GET');
            $response = $app->handle($request);
            $content = $response->getContent();
            
            // Check if Vite assets are being loaded
            $hasViteAssets = strpos($content, '@vite') !== false || 
                           strpos($content, '/build/assets/') !== false ||
                           strpos($content, 'vite') !== false;
            
            // Check for CSS file references
            $hasCSSReferences = strpos($content, '.css') !== false;
            
            // Check for specific asset files
            $hasAppCSS = strpos($content, 'app.css') !== false || strpos($content, 'app-') !== false;
            $hasFrontendCSS = strpos($content, 'frontend.css') !== false || strpos($content, 'frontend-') !== false;
            $hasAdminCSS = strpos($content, 'admin.css') !== false || strpos($content, 'admin-') !== false;
            $hasClientCSS = strpos($content, 'client.css') !== false || strpos($content, 'client-') !== false;
            
            echo "  Status: {$response->getStatusCode()}\n";
            echo "  Content Length: " . strlen($content) . " bytes\n";
            echo "  Has Vite Assets: " . ($hasViteAssets ? 'Yes' : 'No') . "\n";
            echo "  Has CSS References: " . ($hasCSSReferences ? 'Yes' : 'No') . "\n";
            echo "  Has app.css: " . ($hasAppCSS ? 'Yes' : 'No') . "\n";
            echo "  Has frontend.css: " . ($hasFrontendCSS ? 'Yes' : 'No') . "\n";
            echo "  Has admin.css: " . ($hasAdminCSS ? 'Yes' : 'No') . "\n";
            echo "  Has client.css: " . ($hasClientCSS ? 'Yes' : 'No') . "\n";
            
            // Extract and show asset references
            if (preg_match_all('/\/build\/assets\/[^"\']+/', $content, $matches)) {
                echo "  Asset Files Found:\n";
                foreach (array_unique($matches[0]) as $asset) {
                    echo "    - {$asset}\n";
                }
            }
            
            // Check if the page contains basic HTML structure
            $hasHtmlStructure = strpos($content, '<html') !== false && 
                              strpos($content, '<head') !== false && 
                              strpos($content, '<body') !== false;
            echo "  Has HTML Structure: " . ($hasHtmlStructure ? 'Yes' : 'No') . "\n";
            
            // Check for Tailwind classes
            $hasTailwindClasses = preg_match('/class="[^"]*(?:bg-|text-|p-|m-|flex|grid)/', $content);
            echo "  Has Tailwind Classes: " . ($hasTailwindClasses ? 'Yes' : 'No') . "\n";
            
        } catch (Exception $e) {
            echo "  ERROR: " . $e->getMessage() . "\n";
        }
        
        echo "\n" . str_repeat("-", 60) . "\n\n";
    }

    // Test if asset files exist
    echo "Checking if compiled asset files exist:\n\n";
    
    $assetFiles = [
        'public/build/assets/app-JlwmLI3t.css',
        'public/build/assets/frontend-2-Ul6gor.css',
        'public/build/assets/admin-Pt-CQbui.css',
        'public/build/assets/client-CPxHxGl5.css',
        'public/build/assets/app-CCLt7DvH.js',
        'public/build/manifest.json'
    ];
    
    foreach ($assetFiles as $file) {
        $exists = file_exists($file);
        $size = $exists ? filesize($file) : 0;
        echo "  {$file}: " . ($exists ? "EXISTS ({$size} bytes)" : "MISSING") . "\n";
    }

} catch (Exception $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
